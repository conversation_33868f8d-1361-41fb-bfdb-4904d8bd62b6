<?php
require_once 'config/config.php';
require_once 'config/database.php';

header('Content-Type: application/json');

$database = new Database();
$db = $database->getConnection();

$receipt_number = $_GET['receipt_number'] ?? '';

if (empty($receipt_number)) {
    echo json_encode(['success' => false, 'message' => 'رقم الوصل مطلوب']);
    exit;
}

try {
    $stmt = $db->prepare("SELECT id FROM goods_entry_receipts WHERE receipt_number = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$receipt_number]);
    $receipt = $stmt->fetch();
    
    if ($receipt) {
        echo json_encode(['success' => true, 'receipt_id' => $receipt['id']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'الوصل غير موجود']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
?>
