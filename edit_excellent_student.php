<?php
require_once 'includes/config.php';

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$section = isset($_GET['section']) ? $_GET['section'] : 'bac_general';
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

if (!$id) {
    header("Location: excellent_students.php?section=$section&year=$year");
    exit;
}

// تحديد الجدول والفئة
$table = '';
$category = '';
$exam_name = '';

switch ($section) {
    case 'bac_general':
        $table = 'excellent_students_bac';
        $category = 'general';
        $exam_name = 'البكالوريا - عام';
        break;
    case 'bac_military':
        $table = 'excellent_students_bac';
        $category = 'military_school';
        $exam_name = 'البكالوريا - أشبال الأمة';
        break;
    case 'bac_special':
        $table = 'excellent_students_bac';
        $category = 'special_needs';
        $exam_name = 'البكالوريا - ذوي الاحتياجات الخاصة';
        break;
    case 'bem_general':
        $table = 'excellent_students_bem';
        $category = 'general';
        $exam_name = 'التعليم المتوسط - عام';
        break;
    case 'bem_military':
        $table = 'excellent_students_bem';
        $category = 'military_school';
        $exam_name = 'التعليم المتوسط - أشبال الأمة';
        break;
    case 'bem_special':
        $table = 'excellent_students_bem';
        $category = 'special_needs';
        $exam_name = 'التعليم المتوسط - ذوي الاحتياجات الخاصة';
        break;
}

$success_message = '';
$error_message = '';

// جلب بيانات الطالب
try {
    $stmt = $db->prepare("SELECT * FROM $table WHERE id = ? AND category = ?");
    $stmt->execute([$id, $category]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        header("Location: excellent_students.php?section=$section&year=$year");
        exit;
    }
} catch (PDOException $e) {
    $error_message = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($student)) {
    try {
        $student_number = (int)$_POST['student_number'];
        $last_name = trim($_POST['last_name']);
        $first_name = trim($_POST['first_name']);
        $birth_date = $_POST['birth_date'];
        $birth_place = trim($_POST['birth_place']);
        $average = (float)$_POST['average'];
        $branch = trim($_POST['branch']);
        $wilaya = trim($_POST['wilaya']);
        $assignment = trim($_POST['assignment']);
        $student_signature = isset($_POST['student_signature']) ? 1 : 0;
        $fingerprint = isset($_POST['fingerprint']) ? 1 : 0;

        // التحقق من صحة البيانات
        if (empty($last_name) || empty($first_name) || empty($birth_date) || empty($birth_place) || empty($branch) || empty($wilaya)) {
            throw new Exception('جميع الحقول الأساسية مطلوبة');
        }

        if ($average < 0 || $average > 20) {
            throw new Exception('المعدل يجب أن يكون بين 0 و 20');
        }

        // التحقق من عدم تكرار رقم الطالب (باستثناء الطالب الحالي)
        $check_stmt = $db->prepare("SELECT id FROM $table WHERE student_number = ? AND category = ? AND exam_year = ? AND id != ?");
        $check_stmt->execute([$student_number, $category, $year, $id]);
        if ($check_stmt->fetch()) {
            throw new Exception('رقم الطالب موجود مسبقاً في هذا القسم والسنة');
        }

        // تحديث البيانات
        $stmt = $db->prepare("UPDATE $table SET 
            student_number = ?, last_name = ?, first_name = ?, birth_date = ?, birth_place = ?, 
            average = ?, branch = ?, wilaya = ?, assignment = ?, student_signature = ?, fingerprint = ?
            WHERE id = ?");

        $stmt->execute([
            $student_number, $last_name, $first_name, $birth_date, $birth_place, 
            $average, $branch, $wilaya, $assignment, $student_signature, $fingerprint, $id
        ]);

        $success_message = 'تم تحديث بيانات الطالب بنجاح';
        
        // إعادة جلب البيانات المحدثة
        $stmt = $db->prepare("SELECT * FROM $table WHERE id = ?");
        $stmt->execute([$id]);
        $student = $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// قائمة الولايات الجزائرية
$wilayas = [
    'أدرار', 'الشلف', 'الأغواط', 'أم البواقي', 'باتنة', 'بجاية', 'بسكرة', 'بشار', 'البليدة', 'البويرة',
    'تمنراست', 'تبسة', 'تلمسان', 'تيارت', 'تيزي وزو', 'الجزائر', 'الجلفة', 'جيجل', 'سطيف', 'سعيدة',
    'سكيكدة', 'سيدي بلعباس', 'عنابة', 'قالمة', 'قسنطينة', 'المدية', 'مستغانم', 'المسيلة', 'معسكر', 'ورقلة',
    'وهران', 'البيض', 'إليزي', 'برج بوعريريج', 'بومرداس', 'الطارف', 'تندوف', 'تيسمسيلت', 'الوادي', 'خنشلة',
    'سوق أهراس', 'تيبازة', 'ميلة', 'عين الدفلى', 'النعامة', 'عين تموشنت', 'غرداية', 'غليزان'
];

// شعب البكالوريا والتعليم المتوسط
$branches = [];
if (strpos($section, 'bac') !== false) {
    $branches = ['علوم تجريبية', 'رياضيات', 'تقني رياضي', 'تسيير واقتصاد', 'آداب وفلسفة', 'لغات أجنبية'];
} else {
    $branches = ['عام'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات الطالب - <?= $exam_name ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
            max-width: 800px;
            margin: 20px auto;
        }
        .header-section {
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-custom {
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn-primary-custom {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
        }
        .btn-secondary-custom {
            background: #6c757d;
            border: none;
            color: white;
        }
        .checkbox-group {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .form-check {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .required {
            color: #dc3545;
        }
        .student-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <h1><i class="fas fa-user-edit"></i> تعديل بيانات الطالب</h1>
            <p class="mb-0"><?= $exam_name ?> - دورة <?= $year ?></p>
        </div>

        <?php if (isset($student)): ?>
            <div class="student-info">
                <h6><i class="fas fa-info-circle"></i> بيانات الطالب الحالية:</h6>
                <p class="mb-0"><strong><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></strong> - المعدل: <span class="badge bg-success"><?= $student['average'] ?></span></p>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= $success_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($student)): ?>
        <form method="POST" action="">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="student_number" class="form-label">الرقم <span class="required">*</span></label>
                        <input type="number" class="form-control" id="student_number" name="student_number" 
                               value="<?= $student['student_number'] ?>" required min="1">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="average" class="form-label">المعدل <span class="required">*</span></label>
                        <input type="number" class="form-control" id="average" name="average" 
                               value="<?= $student['average'] ?>" required min="0" max="20" step="0.01">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="last_name" class="form-label">اللقب <span class="required">*</span></label>
                        <input type="text" class="form-control" id="last_name" name="last_name" 
                               value="<?= htmlspecialchars($student['last_name']) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="first_name" class="form-label">الاسم <span class="required">*</span></label>
                        <input type="text" class="form-control" id="first_name" name="first_name" 
                               value="<?= htmlspecialchars($student['first_name']) ?>" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="birth_date" class="form-label">تاريخ الميلاد <span class="required">*</span></label>
                        <input type="date" class="form-control" id="birth_date" name="birth_date" 
                               value="<?= $student['birth_date'] ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="birth_place" class="form-label">مكان الميلاد <span class="required">*</span></label>
                        <input type="text" class="form-control" id="birth_place" name="birth_place" 
                               value="<?= htmlspecialchars($student['birth_place']) ?>" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="branch" class="form-label">الشعبة <span class="required">*</span></label>
                        <select class="form-select" id="branch" name="branch" required>
                            <option value="">اختر الشعبة</option>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?= $branch ?>" <?= $student['branch'] == $branch ? 'selected' : '' ?>>
                                    <?= $branch ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="wilaya" class="form-label">الولاية <span class="required">*</span></label>
                        <select class="form-select" id="wilaya" name="wilaya" required>
                            <option value="">اختر الولاية</option>
                            <?php foreach ($wilayas as $wilaya): ?>
                                <option value="<?= $wilaya ?>" <?= $student['wilaya'] == $wilaya ? 'selected' : '' ?>>
                                    <?= $wilaya ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="assignment" class="form-label">التعيين</label>
                <textarea class="form-control" id="assignment" name="assignment" rows="3" 
                          placeholder="مكان التعيين أو الجامعة..."><?= htmlspecialchars($student['assignment']) ?></textarea>
            </div>

            <div class="form-group">
                <label class="form-label">الحالة</label>
                <div class="checkbox-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="student_signature" name="student_signature" 
                               <?= $student['student_signature'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="student_signature">
                            <i class="fas fa-signature"></i> إمضاء المعني
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="fingerprint" name="fingerprint" 
                               <?= $student['fingerprint'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="fingerprint">
                            <i class="fas fa-fingerprint"></i> البصمة
                        </label>
                    </div>
                </div>
            </div>

            <div class="d-flex gap-3 justify-content-center mt-4">
                <button type="submit" class="btn btn-custom btn-primary-custom">
                    <i class="fas fa-save"></i> حفظ التعديلات
                </button>
                <a href="excellent_students.php?section=<?= $section ?>&year=<?= $year ?>" class="btn btn-custom btn-secondary-custom">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </form>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
