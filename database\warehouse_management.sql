-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: 29 يونيو 2025 الساعة 17:33
-- إصدار الخادم: 9.2.0
-- PHP Version: 8.4.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `warehouse_management`
--
CREATE DATABASE IF NOT EXISTS `warehouse_management` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `warehouse_management`;

-- --------------------------------------------------------

--
-- بنية الجدول `alert_settings`
--

DROP TABLE IF EXISTS `alert_settings`;
CREATE TABLE IF NOT EXISTS `alert_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `minimum_stock` decimal(10,2) NOT NULL,
  `alert_enabled` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_item_name` (`item_name`(250))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `consultations`
--

DROP TABLE IF EXISTS `consultations`;
CREATE TABLE IF NOT EXISTS `consultations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `consultation_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `consultation_date` date NOT NULL,
  `country` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'الجزائر',
  `custom_message` text COLLATE utf8mb4_unicode_ci,
  `items` json DEFAULT NULL,
  `total_amount` decimal(12,2) DEFAULT '0.00',
  `currency_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT 'DZD',
  `vat_rate` enum('15','19') COLLATE utf8mb4_unicode_ci DEFAULT '19',
  `signature_enabled` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `consultation_number` (`consultation_number`),
  KEY `idx_consultation_number` (`consultation_number`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `currencies`
--

DROP TABLE IF EXISTS `currencies`;
CREATE TABLE IF NOT EXISTS `currencies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_en` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `symbol` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=MyISAM AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `currencies`
--

INSERT INTO `currencies` (`id`, `code`, `name_ar`, `name_en`, `symbol`, `is_active`) VALUES
(1, 'DZD', 'دينار جزائري', 'Algerian Dinar', 'دج', 1),
(2, 'USD', 'دولار أمريكي', 'US Dollar', '$', 1),
(3, 'EUR', 'يورو', 'Euro', '€', 1),
(4, 'GBP', 'جنيه إسترليني', 'British Pound', '£', 1),
(5, 'JPY', 'ين ياباني', 'Japanese Yen', '¥', 1),
(6, 'RUB', 'روبل روسي', 'Russian Ruble', '₽', 1),
(7, 'SGD', 'دولار سنغافوري', 'Singapore Dollar', 'S$', 1),
(8, 'TRY', 'ليرة تركية', 'Turkish Lira', '₺', 1),
(9, 'LBP', 'ليرة لبنانية', 'Lebanese Pound', 'ل.ل', 1),
(10, 'ATS', 'شيلينغ نمساوي', 'Austrian Schilling', 'S', 1),
(11, 'MZN', 'متيكال موزمبيقي', 'Mozambican Metical', 'MT', 1),
(12, 'ZWL', 'دولار زيمبابوي', 'Zimbabwean Dollar', 'Z$', 1),
(13, 'ZMW', 'كواشا زامبي', 'Zambian Kwacha', 'ZK', 1);

-- --------------------------------------------------------

--
-- بنية الجدول `departments_services_offices`
--

DROP TABLE IF EXISTS `departments_services_offices`;
CREATE TABLE IF NOT EXISTS `departments_services_offices` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('مديرية','مصلحة','مكتب','خلية') COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `idx_name` (`name`(250)),
  KEY `idx_type` (`type`)
) ENGINE=MyISAM AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `departments_services_offices`
--

INSERT INTO `departments_services_offices` (`id`, `name`, `type`, `parent_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'مدير الديوان', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(2, 'الأمانة الخاصة', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(3, 'الأمين العام', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(4, 'الأمانة العامة', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(5, 'المدير الفرعي للادارة العامة', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(6, 'امانة المدير الفرعي للادارة العامة', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(7, 'المدير الفرعي للبكالوريا', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(8, 'امانة المدير الفرعي للبكالوريا', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(9, 'المدير الفرعي للامتحانات و المسابقات المدرسية', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(10, 'المدير الفرعي للامتحانات و المسابقات المهنية', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(11, 'المديرية الفرعية للدراسات و التقويم', 'مديرية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(12, 'رئيس مصلحة الادارة العامة', 'مصلحة', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(13, 'رئيس مصلحة الموظفين', 'مصلحة', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(14, 'رئيس مصلحة الايرادات و النفقات', 'مصلحة', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(15, 'رئيس مصلحة الوسائل', 'مصلحة', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(16, 'رئيس مصلحة الشهادات', 'مصلحة', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(17, 'مكتب العون المحاسب', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(18, 'مكتب الوكيل', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(19, 'مكتب الالتزامات', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(20, 'مكتب الصفقات', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(21, 'مكتب دفع الاجور', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(22, 'مكتب المخزني', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(23, 'الخلية المركزية', 'خلية', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(24, 'مكتب الامتحانات المدرسية', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(25, 'مكتب الامتحانات المهنية', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(26, 'مكتب الاعلام الآلي', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30'),
(27, 'قاعة الاستقبال و التوجيه', 'مكتب', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30');

-- --------------------------------------------------------

--
-- بنية الجدول `directorates_services`
--

DROP TABLE IF EXISTS `directorates_services`;
CREATE TABLE IF NOT EXISTS `directorates_services` (
  `id` int NOT NULL AUTO_INCREMENT,
  `directorate_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `directorate_type` enum('مديرية','مصلحة') COLLATE utf8mb4_unicode_ci DEFAULT 'مديرية',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `directorate_name` (`directorate_name`),
  KEY `idx_directorate_name` (`directorate_name`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `directorates_services`
--

INSERT INTO `directorates_services` (`id`, `directorate_name`, `directorate_type`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'مديرية الامتحانات', 'مديرية', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(2, 'مديرية المسابقات', 'مديرية', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(3, 'مديرية الإدارة العامة', 'مديرية', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(4, 'مديرية الوسائل', 'مديرية', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(5, 'مديرية التكنولوجيا', 'مديرية', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(6, 'مصلحة المحاسبة', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(7, 'مصلحة الموارد البشرية', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(8, 'مصلحة الصيانة', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(9, 'مصلحة الأمن', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(10, 'مصلحة الاتصالات', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(11, 'مصلحة الأرشيف', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40'),
(12, 'مصلحة النقل', 'مصلحة', 1, '2025-06-27 02:05:40', '2025-06-27 02:05:40');

-- --------------------------------------------------------

--
-- بنية الجدول `general_inventory`
--

DROP TABLE IF EXISTS `general_inventory`;
CREATE TABLE IF NOT EXISTS `general_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `inventory_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `registration_date` date NOT NULL,
  `designation` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `value` decimal(12,2) NOT NULL,
  `currency_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT 'DZD',
  `allocation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `office_id` int DEFAULT NULL,
  `exit_info` text COLLATE utf8mb4_unicode_ci,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `inventory_number` (`inventory_number`),
  KEY `idx_inventory_number` (`inventory_number`),
  KEY `idx_designation` (`designation`(250)),
  KEY `office_id` (`office_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `goods_entry_items`
--

DROP TABLE IF EXISTS `goods_entry_items`;
CREATE TABLE IF NOT EXISTS `goods_entry_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `receipt_id` int NOT NULL,
  `item_number` int NOT NULL,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `inventory_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT '1.00',
  `unit_price` decimal(15,2) NOT NULL,
  `total_amount` decimal(15,2) NOT NULL,
  `currency_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'DZD',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_item_number` (`item_number`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `goods_entry_receipts`
--

DROP TABLE IF EXISTS `goods_entry_receipts`;
CREATE TABLE IF NOT EXISTS `goods_entry_receipts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `receipt_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `directorate_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'المديرية الفرعية للادارة العامة',
  `supplier_id` int DEFAULT NULL,
  `receipt_date` date NOT NULL,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `inventory_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `use_item_number` tinyint(1) DEFAULT '1',
  `unit_price` decimal(10,2) NOT NULL,
  `currency_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT 'DZD',
  `total_amount` decimal(10,2) NOT NULL,
  `amount_without_tax` decimal(10,2) NOT NULL,
  `vat_rate` enum('15','19') COLLATE utf8mb4_unicode_ci DEFAULT '19',
  `vat_amount` decimal(10,2) NOT NULL,
  `total_with_tax` decimal(10,2) NOT NULL,
  `signature_warehouse_manager` tinyint(1) DEFAULT '0',
  `signature_means_chief` tinyint(1) DEFAULT '0',
  `signature_sub_director` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `total_items` int DEFAULT '0',
  `subtotal` decimal(15,2) DEFAULT '0.00',
  `tax_amount` decimal(15,2) DEFAULT '0.00',
  `grand_total` decimal(15,2) DEFAULT '0.00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `supplier_id` (`supplier_id`),
  KEY `idx_receipt_number` (`receipt_number`),
  KEY `idx_receipt_date` (`receipt_date`),
  KEY `idx_item_name_goods` (`item_name`(250))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_cards`
--

DROP TABLE IF EXISTS `inventory_cards`;
CREATE TABLE IF NOT EXISTS `inventory_cards` (
  `id` int NOT NULL AUTO_INCREMENT,
  `card_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `unit_of_measure` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `entry_exit_receipt` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `supplier_beneficiary` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `entry_date` date DEFAULT NULL,
  `entry_quantity` decimal(10,2) DEFAULT '0.00',
  `exit_date` date DEFAULT NULL,
  `exit_quantity` decimal(10,2) DEFAULT '0.00',
  `current_stock` decimal(10,2) DEFAULT '0.00',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `idx_item_name` (`item_name`(250)),
  KEY `idx_card_number` (`card_number`),
  KEY `idx_entry_date` (`entry_date`),
  KEY `idx_exit_date` (`exit_date`),
  KEY `idx_current_stock` (`current_stock`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `inventory_cards`
--

INSERT INTO `inventory_cards` (`id`, `card_number`, `item_name`, `unit_of_measure`, `entry_exit_receipt`, `supplier_beneficiary`, `entry_date`, `entry_quantity`, `exit_date`, `exit_quantity`, `current_stock`, `notes`, `created_at`, `updated_at`) VALUES
(5, '2', 'rame a3 papier', 'رزمة', '', '', '0000-00-00', 0.00, '0000-00-00', 0.00, 0.00, '', '2025-06-27 07:59:11', '2025-06-27 08:25:12');

-- --------------------------------------------------------

--
-- Stand-in structure for view `low_stock_items`
-- (See below for the actual view)
--
DROP VIEW IF EXISTS `low_stock_items`;
CREATE TABLE IF NOT EXISTS `low_stock_items` (
`current_stock` decimal(10,2)
,`item_name` varchar(255)
,`minimum_stock` decimal(10,2)
,`unit_of_measure` varchar(50)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `monthly_inventory_report`
-- (See below for the actual view)
--
DROP VIEW IF EXISTS `monthly_inventory_report`;
CREATE TABLE IF NOT EXISTS `monthly_inventory_report` (
`month_year` varchar(7)
,`total_entries` bigint
,`total_quantity_in` decimal(32,2)
,`total_quantity_out` decimal(32,2)
);

-- --------------------------------------------------------

--
-- بنية الجدول `national_office_branches`
--

DROP TABLE IF EXISTS `national_office_branches`;
CREATE TABLE IF NOT EXISTS `national_office_branches` (
  `id` int NOT NULL AUTO_INCREMENT,
  `branch_name` enum('فرع الجزائر','فرع وهران','فرع باتنة','فرع بجاية','فرع البليدة','فرع عنابة','فرع أم البواقي','فرع سعيدة','فرع غرداية') COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fax` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `latitude` decimal(10,8) DEFAULT NULL COMMENT 'خط العرض',
  `longitude` decimal(11,8) DEFAULT NULL COMMENT 'خط الطول',
  `manager_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'اسم مدير الفرع',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `working_hours` text COLLATE utf8mb4_unicode_ci COMMENT 'ساعات العمل',
  `services` text COLLATE utf8mb4_unicode_ci COMMENT 'الخدمات المتاحة',
  PRIMARY KEY (`id`),
  UNIQUE KEY `branch_name` (`branch_name`),
  KEY `idx_branch_name` (`branch_name`),
  KEY `idx_branches_location` (`latitude`,`longitude`),
  KEY `idx_branches_email` (`email`)
) ENGINE=MyISAM AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `national_office_branches`
--

INSERT INTO `national_office_branches` (`id`, `branch_name`, `phone`, `fax`, `address`, `is_active`, `created_at`, `updated_at`, `latitude`, `longitude`, `manager_name`, `email`, `working_hours`, `services`) VALUES
(1, 'فرع الجزائر', '', '', 'alger centre', 1, '2025-06-26 21:45:30', '2025-06-27 16:55:26', 36.75380000, 3.05880000, 'أحمد بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'),
(2, 'فرع وهران', '023665897', '023665898', 'batna centre 23 rue mohamed salah', 1, '2025-06-26 21:45:30', '2025-06-29 06:43:00', 35.69690000, -0.63310000, 'محمد بن يوسف', '<EMAIL>', 'الأحد - الخميس: 16:30 - 08:00', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'),
(3, 'فرع باتنة', '023665899', '023554877', 'batna centre', 1, '2025-06-26 21:45:30', '2025-06-27 16:56:10', 35.55590000, 6.17420000, 'عبد الرحمن بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'),
(4, 'فرع بجاية', '', '', NULL, 1, '2025-06-26 21:45:30', '2025-06-27 15:53:57', 36.75250000, 5.06890000, 'فاطمة بنت أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'),
(5, 'فرع البليدة', '', '', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30', NULL, NULL, NULL, NULL, NULL, NULL),
(6, 'فرع عنابة', '', '', NULL, 1, '2025-06-26 21:45:30', '2025-06-27 15:53:57', 36.90000000, 7.76670000, 'عائشة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'),
(7, 'فرع أم البواقي', '', '', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30', NULL, NULL, NULL, NULL, NULL, NULL),
(8, 'فرع سعيدة', '', '', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30', NULL, NULL, NULL, NULL, NULL, NULL),
(9, 'فرع غرداية', '', '', NULL, 1, '2025-06-26 21:45:30', '2025-06-26 21:45:30', NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- بنية الجدول `office_supplies_requests`
--

DROP TABLE IF EXISTS `office_supplies_requests`;
CREATE TABLE IF NOT EXISTS `office_supplies_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `receipt_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `beneficiary_directorate` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `recipient_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_date` date NOT NULL,
  `materials` json DEFAULT NULL,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `unit` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `supply_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `signature_means_chief` tinyint(1) DEFAULT '0',
  `signature_sub_director` tinyint(1) DEFAULT '0',
  `signature_warehouse_manager` tinyint(1) DEFAULT '0',
  `signature_beneficiary` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bold_text` tinyint(1) DEFAULT '0' COMMENT 'نص عريض (0=عادي، 1=عريض)',
  `text_align` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT 'right' COMMENT 'محاذاة النص (right, center, left)',
  `font_color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'black' COMMENT 'لون الخط (black, blue, red, green, brown, purple)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `idx_receipt_number` (`receipt_number`),
  KEY `idx_request_date` (`request_date`),
  KEY `idx_item_name_supplies` (`item_name`(250))
) ENGINE=MyISAM AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `office_supplies_requests`
--

INSERT INTO `office_supplies_requests` (`id`, `receipt_number`, `beneficiary_directorate`, `recipient_name`, `request_date`, `materials`, `item_name`, `quantity`, `unit`, `supply_number`, `notes`, `signature_means_chief`, `signature_sub_director`, `signature_warehouse_manager`, `signature_beneficiary`, `created_at`, `updated_at`, `bold_text`, `text_align`, `font_color`) VALUES
(6, '2', 'الأمانة الخاصة', 'جون كلارك', '2025-06-27', '[{\"notes\": \"\", \"quantity\": 4, \"item_name\": \"rame papier a3\", \"supply_number\": \"2\"}, {\"notes\": \"\", \"quantity\": 2, \"item_name\": \"rame autocollant diff couleur (rame 20F)\", \"supply_number\": \"3\"}, {\"notes\": \"\", \"quantity\": 4, \"item_name\": \"registre 2 main\", \"supply_number\": \"23\"}, {\"notes\": \"\", \"quantity\": 2, \"item_name\": \"cube mumo\", \"supply_number\": \"49\"}, {\"notes\": \"\", \"quantity\": 2, \"item_name\": \"tonor imprimante 6030\", \"supply_number\": \"54\"}, {\"notes\": \"\", \"quantity\": 1, \"item_name\": \"toner imprimante 6670-dn\", \"supply_number\": \"61\"}, {\"notes\": \"\", \"quantity\": 15, \"item_name\": \"stylo a bille\", \"supply_number\": \"123\"}, {\"notes\": \"\", \"quantity\": 8, \"item_name\": \"stabilo\", \"supply_number\": \"124\"}, {\"notes\": \"\", \"quantity\": 5, \"item_name\": \"crayan noir\", \"supply_number\": \"130\"}, {\"notes\": \"\", \"quantity\": 5, \"item_name\": \"markeur fluerecent\", \"supply_number\": \"128\"}, {\"notes\": \"\", \"quantity\": 6, \"item_name\": \"scotche pm\", \"supply_number\": \"138\"}, {\"notes\": \"\", \"quantity\": 2, \"item_name\": \"classeur pochette\", \"supply_number\": \"145\"}, {\"notes\": \"\", \"quantity\": 4, \"item_name\": \"ciseaux\", \"supply_number\": \"194\"}, {\"notes\": \"\", \"quantity\": 4, \"item_name\": \"ote agrafe\", \"supply_number\": \"151\"}, {\"notes\": \"\", \"quantity\": 2, \"item_name\": \"agrapheuse 24/6\", \"supply_number\": \"120\"}, {\"notes\": \"\", \"quantity\": 5, \"item_name\": \"regle 30cm\", \"supply_number\": \"205\"}]', '', 0.00, NULL, NULL, '', 1, 1, 1, 1, '2025-06-27 10:46:03', '2025-06-27 15:31:22', 1, 'right', 'black'),
(18, '8888', 'الخلية المركزية', 'hmitoche', '2025-06-29', '[{\"notes\": \"\", \"quantity\": 20, \"item_name\": \"rame papier a4\", \"supply_number\": \"1\"}]', '', 0.00, NULL, NULL, '', 1, 1, 1, 1, '2025-06-29 11:17:52', '2025-06-29 11:17:52', 1, 'center', 'black');

-- --------------------------------------------------------

--
-- بنية الجدول `suppliers`
--

DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE IF NOT EXISTS `suppliers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `full_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `commercial_register` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_postal_account` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tax_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `statistical_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fax` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_full_name` (`full_name`(250)),
  KEY `idx_email` (`email`),
  KEY `idx_supplier_name` (`full_name`(250))
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `suppliers`
--

INSERT INTO `suppliers` (`id`, `full_name`, `commercial_register`, `bank_postal_account`, `tax_id`, `statistical_id`, `address`, `phone`, `fax`, `email`, `created_at`, `updated_at`) VALUES
(1, 'data tools', '***********', '023125684769892235', '5587452', '*********', '', '+************', '', '<EMAIL>', '2025-06-29 14:56:15', '2025-06-29 14:56:15');

-- --------------------------------------------------------

--
-- بنية الجدول `transfer_vouchers`
--

DROP TABLE IF EXISTS `transfer_vouchers`;
CREATE TABLE IF NOT EXISTS `transfer_vouchers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `receipt_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `branch_name` enum('فرع الجزائر','فرع وهران','فرع باتنة','فرع بجاية','فرع البليدة','فرع عنابة','فرع أم البواقي','فرع سعيدة','فرع غرداية') COLLATE utf8mb4_unicode_ci NOT NULL,
  `transfer_date` date NOT NULL,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `supply_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `signature_means_chief` tinyint(1) DEFAULT '0',
  `signature_sub_director` tinyint(1) DEFAULT '0',
  `signature_warehouse_manager` tinyint(1) DEFAULT '0',
  `signature_branch_director` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `idx_receipt_number` (`receipt_number`),
  KEY `idx_branch_name` (`branch_name`),
  KEY `idx_item_name_transfer` (`item_name`(250))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `warehouse_supplies`
--

DROP TABLE IF EXISTS `warehouse_supplies`;
CREATE TABLE IF NOT EXISTS `warehouse_supplies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_item_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` enum('لوازم الورق','مستهلكات الطبع والنسخ','اللوازم المكتبية','اللوازم الصيدلانية والوقائية','لوازم السيارات ولواحقها','الأثاث','خاص بمتفوقي الامتحانات المدرسية') COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_number` (`item_number`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `warehouse_supplies`
--

INSERT INTO `warehouse_supplies` (`id`, `item_name`, `item_number`, `old_item_number`, `category`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'rame a4 papier', '1', '', 'لوازم الورق', '', '2025-06-27 09:17:02', '2025-06-27 09:17:02');

-- --------------------------------------------------------

--
-- Structure for view `low_stock_items`
--
DROP TABLE IF EXISTS `low_stock_items`;

DROP VIEW IF EXISTS `low_stock_items`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `low_stock_items`  AS SELECT `ic`.`item_name` AS `item_name`, `ic`.`current_stock` AS `current_stock`, `als`.`minimum_stock` AS `minimum_stock`, `ic`.`unit_of_measure` AS `unit_of_measure` FROM (`inventory_cards` `ic` join `alert_settings` `als` on((`ic`.`item_name` = `als`.`item_name`))) WHERE ((`ic`.`current_stock` <= `als`.`minimum_stock`) AND (`als`.`alert_enabled` = true)) ;

-- --------------------------------------------------------

--
-- Structure for view `monthly_inventory_report`
--
DROP TABLE IF EXISTS `monthly_inventory_report`;

DROP VIEW IF EXISTS `monthly_inventory_report`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `monthly_inventory_report`  AS SELECT date_format(`inventory_cards`.`entry_date`,'%Y-%m') AS `month_year`, count(0) AS `total_entries`, sum(`inventory_cards`.`entry_quantity`) AS `total_quantity_in`, sum(`inventory_cards`.`exit_quantity`) AS `total_quantity_out` FROM `inventory_cards` WHERE (`inventory_cards`.`entry_date` is not null) GROUP BY date_format(`inventory_cards`.`entry_date`,'%Y-%m') ORDER BY `month_year` DESC ;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
