<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? 'medium';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// تحديد أحجام الخط
$font_sizes = [
    'small' => [
        'base' => '10px',
        'header' => '14px',
        'title' => '16px',
        'table' => '9px',
        'padding' => '4px'
    ],
    'medium' => [
        'base' => '12px',
        'header' => '16px',
        'title' => '18px',
        'table' => '11px',
        'padding' => '6px'
    ],
    'large' => [
        'base' => '14px',
        'header' => '18px',
        'title' => '20px',
        'table' => '13px',
        'padding' => '8px'
    ]
];

$current_size = $font_sizes[$font_size] ?? $font_sizes['medium'];

// تحديد حجم الخط تلقائياً حسب عدد المواد
$materials_count = count($materials);
if ($materials_count > 15) {
    $current_size = $font_sizes['small'];
} elseif ($materials_count > 8) {
    $current_size = $font_sizes['medium'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة طلب اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 15px;
            direction: rtl;
            font-size: <?php echo $current_size['base']; ?>;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        .header h1 {
            color: #000;
            font-weight: bold;
            margin: 3px 0;
            font-size: calc(<?php echo $current_size['header']; ?> + 2px);
            text-shadow: 0.5px 0.5px 0px #333;
        }
        .header h2 {
            color: #000;
            font-weight: bold;
            margin: 3px 0;
            font-size: calc(<?php echo $current_size['header']; ?> + 1px);
            text-shadow: 0.5px 0.5px 0px #333;
        }
        .header h3 {
            color: #000;
            font-weight: bold;
            margin: 3px 0;
            font-size: <?php echo $current_size['header']; ?>;
            text-shadow: 0.5px 0.5px 0px #333;
        }
        .form-title { 
            text-align: center; 
            font-size: <?php echo $current_size['title']; ?>; 
            font-weight: bold; 
            margin: 15px 0; 
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .info-section {
            margin: 10px 0;
            padding: <?php echo $current_size['padding']; ?>;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info-row {
            display: flex;
            margin: 5px 0;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
            color: #2c5530;
            font-size: <?php echo $current_size['base']; ?>;
        }
        .info-value {
            flex: 1;
            padding: 4px;
            border-bottom: 1px solid #ddd;
            font-size: <?php echo $current_size['base']; ?>;
        }
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .materials-table th,
        .materials-table td {
            border: 1px solid #000;
            padding: <?php echo $current_size['padding']; ?>;
            text-align: center;
            font-size: <?php echo $current_size['table']; ?>;
        }
        .materials-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .signatures-section {
            margin-top: 30px;
            page-break-inside: avoid;
        }
        .signature-grid {
            display: grid;
            grid-template-columns: 8% 45% 12% 18% 17%;
            gap: 0;
            margin-top: 30px;
        }
        .signature-box {
            border: none;
            padding: 15px 3px;
            text-align: center;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: <?php echo ($current_size['base'] - 2) . 'px'; ?>;
            font-weight: bold;
            color: #000;
            margin: 8px 1px;
            white-space: normal;
            overflow: visible;
            word-wrap: break-word;
            line-height: 1.3;
        }
        .signature-box.span-2 {
            grid-column: span 2;
        }
        .signature-box.span-3 {
            grid-column: span 3;
        }
        @media print {
            body {
                margin: 2mm !important;
                padding: 0 !important;
                font-size: <?php echo $current_size['table']; ?>;
                line-height: 1.1;
            }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
            .header {
                margin-bottom: 3px;
                padding-bottom: 2px;
                margin-top: 0;
            }
            .header h1, .header h2, .header h3 {
                margin: 1px 0 !important;
                padding: 0 !important;
            }
            .info-section {
                margin: 2px 0;
                padding: 2px;
                border: none;
            }
            .materials-table {
                margin: 3px 0 !important;
                border-collapse: collapse;
            }
            .materials-table th, .materials-table td {
                padding: 1px 2px;
                border: 1px solid #000;
            }
            .signature-box {
                padding: 6px 3px;
                min-height: 30px;
                margin: 3px 0;
                font-size: <?php echo ($current_size['base'] - 2) . 'px'; ?>;
                font-weight: bold !important;
                color: #000 !important;
                white-space: normal !important;
                overflow: visible !important;
                word-wrap: break-word !important;
                line-height: 1.2 !important;
                text-align: center !important;
                border: none !important;
            }
            .signature-grid {
                margin-top: 8px;
                margin-bottom: 10px;
            }
            /* تقليل المسافة فوق جدول المعلومات */
            table[style*="border-collapse"] {
                margin-top: 0 !important;
                margin-bottom: 0 !important;
            }
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            font-size: calc(<?php echo $current_size['table']; ?> - 1px);
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
        <h2><?php echo MINISTRY_NAME_AR; ?></h2>
        <h3><?php echo OFFICE_NAME_AR; ?></h3>
    </div>
    

    


    <!-- معلومات فوق الجدول: رقم الوصل والمديرية (يسار) + التاريخ والمستلم (يمين) -->
    <table style="width: 100%; border-collapse: collapse; margin: 10px 0; table-layout: fixed;">
        <tr>
            <!-- معلومات اليسار: رقم الوصل والمديرية -->
            <td style="width: 50%; text-align: right; padding: 5px; font-size: <?php echo ($current_size['base'] - 1) . 'px'; ?>; font-weight: bold; vertical-align: top;">
                رقم الوصل: <?php echo htmlspecialchars($request['receipt_number']); ?><br>
                المديرية أو المصلحة المستفيدة: <?php echo htmlspecialchars($request['beneficiary_directorate']); ?>
            </td>
            <!-- معلومات اليمين: التاريخ والمستلم -->
            <td style="width: 50%; text-align: left; padding: 5px; font-size: <?php echo ($current_size['base'] - 1) . 'px'; ?>; font-weight: bold; vertical-align: top;">
                التاريخ: <?php echo formatArabicDate($request['request_date']); ?><br>
                المستلم: <?php echo htmlspecialchars($request['recipient_name']); ?>
            </td>
        </tr>
    </table>

    <!-- جدول المواد -->
    <table class="materials-table">
        <thead>
            <tr>
                <th width="8%">الرقم</th>
                <th width="45%">اسم المادة</th>
                <th width="12%">الكمية</th>
                <th width="18%">رقم اللوازم</th>
                <th width="17%">ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($materials)): ?>
                <tr>
                    <td colspan="5">لا توجد مواد مسجلة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($materials as $index => $material): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td style="text-align: right; font-weight: bold;">
                            <?php echo htmlspecialchars($material['item_name']); ?>
                        </td>
                        <td>
                            <strong><?php echo number_format($material['quantity'], 0); ?></strong>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($material['supply_number'] ?: '-'); ?>
                        </td>
                        <td>-</td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <!-- الملاحظات -->
    <?php if ($request['notes']): ?>
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">الملاحظات:</div>
                <div class="info-value"><?php echo nl2br(htmlspecialchars($request['notes'])); ?></div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- التوقيعات الأفقية 2x2 -->
    <div class="signatures-section">
        
        <!-- الصف الأول: رئيس مصلحة الوسائل + المدير الفرعي -->
        <div class="signature-grid">
            <!-- رئيس مصلحة الوسائل (العمود الأول - الرقم) -->
            <div class="signature-box">
                إمضاء وختم رئيس مصلحة الوسائل
            </div>
            <!-- أعمدة فارغة (اسم المادة + الكمية + رقم اللوازم) -->
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <!-- المدير الفرعي (العمود الأخير - الملاحظات) -->
            <div class="signature-box">
                إمضاء وختم المدير الفرعي للإدارة العامة
            </div>
        </div>

        <!-- الصف الثاني: المكلف بتسيير المخزن + المستفيد -->
        <div class="signature-grid" style="margin-top: 50px;">
            <!-- المكلف بتسيير المخزن (العمود الأول - الرقم) -->
            <div class="signature-box">
                إمضاء وختم المكلف بتسيير المخزن
            </div>
            <!-- أعمدة فارغة (اسم المادة + الكمية + رقم اللوازم) -->
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <!-- المستفيد (العمود الأخير - الملاحظات) -->
            <div class="signature-box">
                إمضاء وختم المستفيد
            </div>
        </div>
    </div>
    
    <!-- تذييل -->
    <div class="footer">
        تم إنشاء هذا المستند تلقائياً من نظام إدارة اللوازم المكتبية - <?php echo date('Y-m-d H:i:s'); ?>
        <br>عدد المواد: <?php echo count($materials); ?> | حجم الخط: <?php echo $font_size; ?>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <div class="btn-group mb-3">
            <a href="?id=<?php echo $id; ?>&font_size=small" class="btn btn-outline-secondary <?php echo $font_size == 'small' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>خط صغير
            </a>
            <a href="?id=<?php echo $id; ?>&font_size=medium" class="btn btn-outline-secondary <?php echo $font_size == 'medium' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>خط متوسط
            </a>
            <a href="?id=<?php echo $id; ?>&font_size=large" class="btn btn-outline-secondary <?php echo $font_size == 'large' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>خط كبير
            </a>
        </div>
        <br>
        <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
        <a href="office_supplies.php" class="btn btn-info btn-lg">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <script>
        // طباعة تلقائية عند اختيار حجم خط جديد
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() { 
                setTimeout(() => window.print(), 500); 
            }
        }
    </script>
</body>
</html>
