<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة الممونين - العرض العمودي (Portrait)</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            background: #2c3e50;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .improvement-demo {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        
        .before {
            background: #ffebee;
            border: 1px solid #f44336;
        }
        
        .after {
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        
        .success {
            background: #c8e6c9;
            color: #2e7d32;
        }
        
        .page-demo {
            border: 2px solid #333;
            margin: 10px 0;
            padding: 10px;
            text-align: center;
            font-size: 12px;
        }
        
        .landscape-demo {
            width: 200px;
            height: 140px;
            background: #ffebee;
        }
        
        .portrait-demo {
            width: 140px;
            height: 200px;
            background: #e8f5e8;
        }
        
        .table-demo {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 10px;
        }
        
        .table-demo th,
        .table-demo td {
            border: 1px solid #333;
            padding: 4px 2px;
            text-align: center;
        }
        
        .table-demo th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .highlight-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="test-container">
    <div class="test-title">📄 تغيير طباعة الممونين إلى العرض العمودي (Portrait)</div>
    
    <h3>✅ التحسين المطبق:</h3>
    <div class="improvement-demo">
        <div class="status success">✅ تم تغيير اتجاه الطباعة بنجاح</div>
        
        <h4>🎯 الهدف من التغيير:</h4>
        <ul>
            <li>✅ <strong>استخدام أفضل للمساحة:</strong> العرض العمودي أنسب لجداول الممونين</li>
            <li>✅ <strong>قراءة أسهل:</strong> النصوص الطويلة تظهر بشكل أفضل</li>
            <li>✅ <strong>توافق مع المعايير:</strong> معظم الوثائق الرسمية تطبع عمودياً</li>
            <li>✅ <strong>توفير الورق:</strong> استغلال أمثل لحجم الورقة</li>
        </ul>
        
        <h4>🔧 التغييرات المطبقة:</h4>
        <ul>
            <li>✅ إضافة <code>@page { size: A4 portrait; }</code> في CSS</li>
            <li>✅ تحسين حجم الخط للطباعة العمودية</li>
            <li>✅ إخفاء الأعمدة غير الضرورية (الإجراءات)</li>
            <li>✅ تحسين عرض النصوص الطويلة</li>
            <li>✅ إضافة دالة طباعة محسنة</li>
        </ul>
    </div>

    <h3>🔍 مقارنة اتجاه الطباعة:</h3>
    
    <div class="comparison">
        <div class="before">
            <h4>❌ قبل التغيير (Landscape)</h4>
            <div class="page-demo landscape-demo">
                <strong>أفقي - Landscape</strong><br>
                297mm × 210mm<br>
                عرض أكبر<br>
                ارتفاع أقل
            </div>
            <p style="color: #c62828; font-size: 12px;">
                ⚠️ المشاكل:<br>
                - مساحة مهدورة على الجانبين<br>
                - صعوبة في قراءة النصوص الطويلة<br>
                - غير مناسب للوثائق الرسمية<br>
                - استهلاك ورق أكثر
            </p>
        </div>
        
        <div class="after">
            <h4>✅ بعد التغيير (Portrait)</h4>
            <div class="page-demo portrait-demo">
                <strong>عمودي - Portrait</strong><br>
                210mm × 297mm<br>
                عرض مناسب<br>
                ارتفاع أكبر
            </div>
            <p style="color: #2e7d32; font-size: 12px;">
                ✅ الفوائد:<br>
                - استغلال أمثل للمساحة<br>
                - قراءة أسهل للنصوص<br>
                - مظهر احترافي ورسمي<br>
                - توفير في الورق
            </p>
        </div>
    </div>

    <h3>📊 مثال على جدول الممونين المحسن:</h3>
    
    <div class="highlight-box">
        <h4 style="text-align: center; margin-bottom: 15px;">🏛️ قائمة الممونين - العرض العمودي</h4>
        
        <table class="table-demo">
            <thead>
                <tr>
                    <th width="25%">الاسم الكامل</th>
                    <th width="15%">رقم الهاتف</th>
                    <th width="25%">البريد الإلكتروني</th>
                    <th width="20%">السجل التجاري</th>
                    <th width="15%">تاريخ الإضافة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>أحمد محمد الجزائري</td>
                    <td>0555123456</td>
                    <td><EMAIL></td>
                    <td>RC123456789</td>
                    <td>2025-01-15</td>
                </tr>
                <tr>
                    <td>فاطمة بن علي</td>
                    <td>0666789123</td>
                    <td><EMAIL></td>
                    <td>RC987654321</td>
                    <td>2025-02-20</td>
                </tr>
                <tr>
                    <td>محمد الطاهر بوعلام</td>
                    <td>0777456789</td>
                    <td><EMAIL></td>
                    <td>RC456789123</td>
                    <td>2025-03-10</td>
                </tr>
                <tr>
                    <td>خديجة عبد الرحمن</td>
                    <td>0555987654</td>
                    <td><EMAIL></td>
                    <td>RC789123456</td>
                    <td>2025-04-05</td>
                </tr>
            </tbody>
        </table>
        
        <p style="font-size: 10px; text-align: center; margin-top: 15px;">
            ✅ الجدول يتسع بشكل مثالي في العرض العمودي
        </p>
    </div>

    <h3>💻 الكود المطبق:</h3>
    
    <h4>📁 في includes/header.php:</h4>
    <div class="code-block">
@media print {
    /* تعيين اتجاه الطباعة للممونين كعمودي (Portrait) */
    @page {
        size: A4 portrait;
        margin: 15mm;
    }
    
    /* تحسين عرض جدول الممونين للطباعة العمودية */
    .table {
        font-size: 10px;
        width: 100%;
    }
    
    .table th,
    .table td {
        padding: 4px 2px;
        font-size: 9px;
        word-wrap: break-word;
        max-width: 80px;
    }
    
    /* إخفاء الأعمدة غير الضرورية في الطباعة */
    .table .no-print-column {
        display: none !important;
    }
    
    /* تحسين عرض النص الطويل */
    .text-truncate-print {
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }
}
    </div>
    
    <h4>📁 في suppliers.php:</h4>
    <div class="code-block">
// دالة الطباعة المحسنة
function printPage() {
    // إخفاء عناصر لا نريد طباعتها
    const elementsToHide = document.querySelectorAll('.no-print, .btn, .alert, .card-header, .form-control, .form-label, form');
    elementsToHide.forEach(element => {
        element.style.display = 'none';
    });
    
    // إضافة عنوان للطباعة
    const printTitle = document.createElement('div');
    printTitle.innerHTML = `
        &lt;div style="text-align: center; margin-bottom: 20px;"&gt;
            &lt;h1&gt;الجمهورية الجزائرية الديمقراطية الشعبية&lt;/h1&gt;
            &lt;h2&gt;وزارة التربية الوطنية&lt;/h2&gt;
            &lt;h3&gt;الديوان الوطني للامتحانات والمسابقات&lt;/h3&gt;
            &lt;h4&gt;قائمة الممونين&lt;/h4&gt;
        &lt;/div&gt;
    `;
    
    // طباعة الصفحة
    window.print();
}
    </div>

    <h3>📊 جدول التحسينات:</h3>
    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
        <tr style="background: #f0f0f0;">
            <th style="border: 1px solid #ddd; padding: 8px;">الجانب</th>
            <th style="border: 1px solid #ddd; padding: 8px;">قبل التغيير (Landscape)</th>
            <th style="border: 1px solid #ddd; padding: 8px;">بعد التغيير (Portrait)</th>
            <th style="border: 1px solid #ddd; padding: 8px;">الفائدة</th>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">اتجاه الطباعة</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">أفقي (297×210)</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">عمودي (210×297)</td>
            <td style="border: 1px solid #ddd; padding: 8px;">استغلال أفضل للمساحة</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">عرض الجدول</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">واسع جداً</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">مناسب ومتوازن</td>
            <td style="border: 1px solid #ddd; padding: 8px;">قراءة أسهل</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">النصوص الطويلة</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">مقطوعة أو مضغوطة</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">واضحة ومقروءة</td>
            <td style="border: 1px solid #ddd; padding: 8px;">وضوح أفضل</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">المظهر الرسمي</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">غير تقليدي</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">احترافي ورسمي</td>
            <td style="border: 1px solid #ddd; padding: 8px;">يليق بالمؤسسات</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">استهلاك الورق</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">أكثر</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">أقل</td>
            <td style="border: 1px solid #ddd; padding: 8px;">توفير في التكلفة</td>
        </tr>
    </table>

    <h3>🧪 كيفية الاختبار:</h3>
    <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; border: 1px solid #2196f3;">
        <ol>
            <li>افتح: <code>http://localhost/Sirius18/suppliers.php</code></li>
            <li>أضف بعض الممونين إذا لم تكن موجودة</li>
            <li>انقر على زر "طباعة" 🖨️</li>
            <li>في معاينة الطباعة، تحقق من:
                <ul>
                    <li>✅ <strong>الاتجاه عمودي</strong> (Portrait)</li>
                    <li>✅ <strong>الجدول يتسع بشكل مثالي</strong></li>
                    <li>✅ <strong>النصوص واضحة ومقروءة</strong></li>
                    <li>✅ <strong>عمود الإجراءات مخفي</strong></li>
                    <li>✅ <strong>رأس المؤسسة ظاهر</strong></li>
                </ul>
            </li>
            <li>اطبع الصفحة فعلياً للتأكد من الجودة</li>
        </ol>
    </div>

    <h3>🎯 الفوائد المحققة:</h3>
    <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; border: 1px solid #4caf50;">
        <ul>
            <li>✅ <strong>استغلال أمثل للمساحة:</strong> الجدول يتسع بشكل مثالي</li>
            <li>✅ <strong>قراءة أسهل:</strong> النصوص الطويلة واضحة</li>
            <li>✅ <strong>مظهر احترافي:</strong> يليق بالمؤسسات الحكومية</li>
            <li>✅ <strong>توفير في الورق:</strong> استهلاك أقل</li>
            <li>✅ <strong>توافق مع المعايير:</strong> الوثائق الرسمية عمودية</li>
            <li>✅ <strong>طباعة محسنة:</strong> جودة أفضل وأوضح</li>
        </ul>
    </div>
</div>

</body>
</html>
