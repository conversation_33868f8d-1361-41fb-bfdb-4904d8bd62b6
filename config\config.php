<?php
/**
 * الإعدادات العامة للنظام
 * نظام تسيير المخزن - الديوان الوطني للامتحانات والمسابقات
 */

// إعدادات النظام الأساسية
define('SITE_NAME', 'نظام تسيير المخزن');
define('SITE_URL', 'http://localhost/Sirius18/');
define('ADMIN_EMAIL', '<EMAIL>');

// معلومات المؤسسة
define('INSTITUTION_NAME_AR', 'الجمهورية الجزائرية الديمقراطية الشعبية');
define('MINISTRY_NAME_AR', 'وزارة التربية الوطنية');
define('OFFICE_NAME_AR', 'الديوان الوطني للامتحانات والمسابقات');

// إعدادات التاريخ والوقت
date_default_timezone_set('Africa/Algiers');

// إعدادات الجلسة
session_start();

// إعدادات الأمان
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB
define('ALLOWED_EXTENSIONS', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png']);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات العملات
define('DEFAULT_CURRENCY', 'DZD');
define('CURRENCY_SYMBOL', 'دج');

// إعدادات الطباعة
define('PRINT_LOGO_PATH', 'assets/images/logo.png');
define('PRINT_HEADER_HEIGHT', '150px');

// إعدادات التنبيهات
define('LOW_STOCK_ALERT', true);
define('EMAIL_ALERTS', true);

// دالة لإنشاء رمز CSRF
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// دالة للتحقق من رمز CSRF
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// دالة لتنظيف البيانات المدخلة
function sanitizeInput($data) {
    if ($data === null) {
        return '';
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة لتحويل التاريخ إلى التنسيق العربي
function formatArabicDate($date) {
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس', '04' => 'أبريل',
        '05' => 'مايو', '06' => 'يونيو', '07' => 'يوليو', '08' => 'أغسطس',
        '09' => 'سبتمبر', '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    $dateObj = new DateTime($date);
    $day = $dateObj->format('d');
    $month = $months[$dateObj->format('m')];
    $year = $dateObj->format('Y');
    
    return "$day $month $year";
}

// دالة لتحويل الأرقام إلى العربية
function convertToArabicNumbers($number) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english_numbers, $arabic_numbers, $number);
}

// دالة لتسجيل الأخطاء
function logError($message, $file = '', $line = '') {
    $log_message = date('Y-m-d H:i:s') . " - Error: $message";
    if ($file) $log_message .= " in $file";
    if ($line) $log_message .= " on line $line";
    $log_message .= PHP_EOL;
    
    error_log($log_message, 3, 'logs/error.log');
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة لإنشاء رقم مرجعي فريد
function generateReferenceNumber($prefix = '') {
    return $prefix . date('Ymd') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}
?>
