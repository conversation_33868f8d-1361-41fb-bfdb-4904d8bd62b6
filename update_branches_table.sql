-- تحديث جدول الفروع لإضافة الحقول الجديدة
-- Update branches table to add new fields

-- إضافة حقول الموقع الجغرافي
ALTER TABLE national_office_branches 
ADD COLUMN latitude DECIMAL(10, 8) NULL COMMENT 'خط العرض',
ADD COLUMN longitude DECIMAL(11, 8) NULL COMMENT 'خط الطول';

-- إضا<PERSON>ة حقول معلومات إضافية
ALTER TABLE national_office_branches 
ADD COLUMN manager_name VARCHAR(100) NULL COMMENT 'اسم مدير الفرع',
ADD COLUMN email VARCHAR(100) NULL COMMENT 'البريد الإلكتروني',
ADD COLUMN working_hours TEXT NULL COMMENT 'ساعات العمل',
ADD COLUMN services TEXT NULL COMMENT 'الخدمات المتاحة';

-- إضافة بيانات تجريبية للفروع الموجودة
UPDATE national_office_branches SET 
    latitude = 36.7538, longitude = 3.0588, 
    manager_name = 'أحمد بن علي', 
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع الجزائر';

UPDATE national_office_branches SET 
    latitude = 35.6969, longitude = -0.6331,
    manager_name = 'محمد بن يوسف',
    email = '<EMAIL>', 
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع وهران';

UPDATE national_office_branches SET 
    latitude = 35.5559, longitude = 6.1742,
    manager_name = 'عبد الرحمن بن محمد',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30', 
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع باتنة';

UPDATE national_office_branches SET 
    latitude = 36.7525, longitude = 5.0689,
    manager_name = 'فاطمة بنت أحمد',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع بجاية';

UPDATE national_office_branches SET 
    latitude = 36.4203, longitude = 2.8329,
    manager_name = 'خالد بن عمر',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع البليدة';

UPDATE national_office_branches SET 
    latitude = 36.9000, longitude = 7.7667,
    manager_name = 'عائشة بنت محمد',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع عنابة';

UPDATE national_office_branches SET 
    latitude = 35.8742, longitude = 7.0917,
    manager_name = 'يوسف بن إبراهيم',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع أم البواقي';

UPDATE national_office_branches SET 
    latitude = 34.8667, longitude = 0.1500,
    manager_name = 'نور الدين بن علي',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع سعيدة';

UPDATE national_office_branches SET 
    latitude = 32.4911, longitude = 3.6736,
    manager_name = 'سعاد بنت عبد الله',
    email = '<EMAIL>',
    working_hours = 'الأحد - الخميس: 8:00 - 16:30',
    services = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
WHERE branch_name = 'فرع غرداية';

-- إنشاء فهرس للبحث الجغرافي
CREATE INDEX idx_branches_location ON national_office_branches(latitude, longitude);

-- إنشاء فهرس للبحث بالبريد الإلكتروني
CREATE INDEX idx_branches_email ON national_office_branches(email);

-- عرض النتيجة النهائية
SELECT 
    id,
    branch_name,
    address,
    latitude,
    longitude,
    manager_name,
    email,
    working_hours,
    is_active
FROM national_office_branches 
ORDER BY branch_name;
