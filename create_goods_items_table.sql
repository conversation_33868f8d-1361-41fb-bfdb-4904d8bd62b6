-- إنشا<PERSON> جدول لمواد وصل دخول السلع
CREATE TABLE IF NOT EXISTS goods_entry_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_id INT NOT NULL,
    item_number INT NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    item_code VARCHAR(100),
    inventory_number VARCHAR(100),
    quantity DECIMAL(10,2) DEFAULT 1.00,
    unit_price DECIMAL(15,2) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    currency_type VARCHAR(10) NOT NULL DEFAULT 'DZD',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (receipt_id) REFERENCES goods_entry_receipts(id) ON DELETE CASCADE,
    INDEX idx_receipt_id (receipt_id),
    INDEX idx_item_number (item_number)
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> أعمدة جديدة للمجاميع (إذا لم تكن موجودة)
ALTER TABLE goods_entry_receipts
ADD COLUMN IF NOT EXISTS total_items INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS subtotal DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS grand_total DECIMAL(15,2) DEFAULT 0.00;

-- تحديث البيانات الموجودة (إذا كانت هناك بيانات قديمة)
UPDATE goods_entry_receipts
SET total_items = 1,
    subtotal = COALESCE(total_amount, 0),
    grand_total = COALESCE(total_amount, 0)
WHERE total_items = 0 AND total_amount IS NOT NULL;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_receipt_date ON goods_entry_receipts(receipt_date);
CREATE INDEX IF NOT EXISTS idx_receipt_number ON goods_entry_receipts(receipt_number);
CREATE INDEX IF NOT EXISTS idx_directorate_name ON goods_entry_receipts(directorate_name);

-- إضافة قيود إضافية
ALTER TABLE goods_entry_items
ADD CONSTRAINT chk_quantity_positive CHECK (quantity > 0),
ADD CONSTRAINT chk_unit_price_positive CHECK (unit_price >= 0),
ADD CONSTRAINT chk_total_amount_positive CHECK (total_amount >= 0);

-- تعليق على الجداول
ALTER TABLE goods_entry_receipts COMMENT = 'جدول وصولات دخول السلع إلى المخزن';
ALTER TABLE goods_entry_items COMMENT = 'جدول مواد وصولات دخول السلع';
