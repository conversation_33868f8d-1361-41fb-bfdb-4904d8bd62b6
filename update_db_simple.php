<?php
require_once 'includes/config.php';

echo "<h2>تحديث قاعدة البيانات - المديريات والمصالح</h2>";

try {
    // التحقق من وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'departments_services_offices'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>الجدول غير موجود. يجب إنشاؤه أولاً.</p>";
        exit;
    }

    // جلب الأعمدة الحالية
    $stmt = $db->query("DESCRIBE departments_services_offices");
    $existing_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }

    echo "<h3>الأعمدة الحالية:</h3>";
    echo "<ul>";
    foreach ($existing_columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";

    // الأعمدة المطلوب إضافتها
    $new_columns = [
        'internal_number' => "VARCHAR(50) DEFAULT NULL COMMENT 'الرقم الداخلي'",
        'rank' => "VARCHAR(100) DEFAULT NULL COMMENT 'الرتبة'",
        'manager_name' => "VARCHAR(255) DEFAULT NULL COMMENT 'اسم المسؤول'",
        'manager_rank' => "VARCHAR(100) DEFAULT NULL COMMENT 'رتبة المسؤول'",
        'internal_phone' => "VARCHAR(20) DEFAULT NULL COMMENT 'الهاتف الداخلي'",
        'email' => "VARCHAR(255) DEFAULT NULL COMMENT 'البريد الإلكتروني'",
        'description' => "TEXT DEFAULT NULL COMMENT 'الوصف'",
        'display_order' => "INT DEFAULT 0 COMMENT 'ترتيب العرض'"
    ];

    echo "<h3>إضافة الأعمدة الجديدة:</h3>";
    
    foreach ($new_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                $sql = "ALTER TABLE departments_services_offices ADD COLUMN $column_name $column_definition";
                $db->exec($sql);
                echo "<p style='color: green;'>✓ تم إضافة العمود: $column_name</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ خطأ في إضافة العمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>- العمود $column_name موجود مسبقاً</p>";
        }
    }

    // إضافة الفهارس
    echo "<h3>إضافة الفهارس:</h3>";
    
    $indexes = [
        'idx_internal_number' => 'internal_number',
        'idx_rank' => 'rank',
        'idx_display_order' => 'display_order'
    ];

    foreach ($indexes as $index_name => $column) {
        try {
            // التحقق من وجود الفهرس
            $stmt = $db->query("SHOW INDEX FROM departments_services_offices WHERE Key_name = '$index_name'");
            if ($stmt->rowCount() == 0) {
                $sql = "CREATE INDEX $index_name ON departments_services_offices ($column)";
                $db->exec($sql);
                echo "<p style='color: green;'>✓ تم إضافة الفهرس: $index_name</p>";
            } else {
                echo "<p style='color: blue;'>- الفهرس $index_name موجود مسبقاً</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في إضافة الفهرس $index_name: " . $e->getMessage() . "</p>";
        }
    }

    echo "<h3>التحقق النهائي:</h3>";
    
    // جلب الأعمدة بعد التحديث
    $stmt = $db->query("DESCRIBE departments_services_offices");
    $final_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $final_columns[] = $row['Field'];
    }

    echo "<p><strong>إجمالي الأعمدة بعد التحديث: " . count($final_columns) . "</strong></p>";
    
    echo "<h4>جميع الأعمدة:</h4>";
    echo "<ul>";
    foreach ($final_columns as $column) {
        $is_new = in_array($column, array_keys($new_columns));
        $style = $is_new ? "color: green; font-weight: bold;" : "";
        echo "<li style='$style'>$column" . ($is_new ? " (جديد)" : "") . "</li>";
    }
    echo "</ul>";

    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0;'>✅ تم تحديث قاعدة البيانات بنجاح!</h3>";
    echo "<p style='color: #155724; margin: 10px 0 0 0;'>يمكنك الآن استخدام جميع الميزات الجديدة في نظام المديريات والمصالح.</p>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ خطأ في قاعدة البيانات</h3>";
    echo "<p style='color: #721c24; margin: 10px 0 0 0;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
ul { padding-right: 20px; }
li { margin: 5px 0; }
</style>
