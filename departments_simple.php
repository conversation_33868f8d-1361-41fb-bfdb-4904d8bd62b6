<?php
require_once 'includes/config.php';

// معالجة العمليات
$success_message = '';
$error_message = '';

// دالة تنظيف المدخلات
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// معالجة الحذف
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';

if ($action === 'delete' && !empty($id)) {
    try {
        // التحقق من وجود وحدات فرعية
        $stmt = $db->prepare("SELECT COUNT(*) FROM departments_services_offices WHERE parent_id = ?");
        $stmt->execute([$id]);
        $child_count = $stmt->fetchColumn();
        
        if ($child_count > 0) {
            $error_message = 'لا يمكن حذف هذه الوحدة لأنها تحتوي على وحدات فرعية';
        } else {
            $stmt = $db->prepare("DELETE FROM departments_services_offices WHERE id = ?");
            $stmt->execute([$id]);
            $success_message = 'تم حذف الوحدة الإدارية بنجاح';
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف الوحدة: ' . $e->getMessage();
    }
}

// معالجة الإضافة والتحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $name = sanitizeInput($_POST['name'] ?? '');
        $type = sanitizeInput($_POST['type'] ?? '');
        $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (empty($name) || empty($type)) {
            $error_message = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE departments_services_offices SET
                    name = ?, type = ?, parent_id = ?, is_active = ?
                    WHERE id = ?");

                $stmt->execute([$name, $type, $parent_id, $is_active, $_POST['id']]);

                $success_message = 'تم تحديث الوحدة الإدارية بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO departments_services_offices
                    (name, type, parent_id, is_active)
                    VALUES (?, ?, ?, ?)");

                $stmt->execute([$name, $type, $parent_id, $is_active]);

                $success_message = 'تم إضافة الوحدة الإدارية بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// جلب بيانات التحديث
$edit_data = [];
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM departments_services_offices WHERE id = ?");
        $stmt->execute([$_GET['edit']]);
        $edit_data = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $error_message = 'خطأ في جلب البيانات: ' . $e->getMessage();
    }
}

// جلب قائمة الوحدات الأساسية للاختيار كوحدة أب
$stmt = $db->query("SELECT id, name, type FROM departments_services_offices 
                   WHERE type IN ('مديرية', 'مصلحة') AND is_active = 1 
                   ORDER BY type, name ASC");
$parent_units = $stmt->fetchAll();

// البحث
$search = $_GET['search'] ?? '';
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE dso.name LIKE ? OR dso.type LIKE ?";
    $params = ["%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT dso.*, parent.name as parent_name, parent.type as parent_type,
                     (SELECT COUNT(*) FROM departments_services_offices WHERE parent_id = dso.id) as child_count
                     FROM departments_services_offices dso
                     LEFT JOIN departments_services_offices parent ON dso.parent_id = parent.id
                     $where_clause
                     ORDER BY dso.type, dso.name ASC");
$stmt->execute($params);
$departments = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة المديريات والمصالح</h3>
                </div>
                <div class="card-body">
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- نموذج الإضافة/التحديث -->
                    <form method="POST" class="mb-4">
                        <?php if (!empty($edit_data)): ?>
                            <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم الوحدة الإدارية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo $edit_data['name'] ?? ''; ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">النوع <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="مديرية" <?php echo ($edit_data['type'] ?? '') === 'مديرية' ? 'selected' : ''; ?>>مديرية</option>
                                    <option value="مصلحة" <?php echo ($edit_data['type'] ?? '') === 'مصلحة' ? 'selected' : ''; ?>>مصلحة</option>
                                    <option value="مكتب" <?php echo ($edit_data['type'] ?? '') === 'مكتب' ? 'selected' : ''; ?>>مكتب</option>
                                    <option value="خلية" <?php echo ($edit_data['type'] ?? '') === 'خلية' ? 'selected' : ''; ?>>خلية</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="parent_id" class="form-label">الوحدة الأب</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">لا توجد (وحدة رئيسية)</option>
                                    <?php foreach ($parent_units as $unit): ?>
                                        <option value="<?php echo $unit['id']; ?>" 
                                                <?php echo ($edit_data['parent_id'] ?? '') == $unit['id'] ? 'selected' : ''; ?>>
                                            <?php echo $unit['type'] . ': ' . $unit['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?php echo ($edit_data['is_active'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <?php if (!empty($edit_data)): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save"></i> تحديث
                                    </button>
                                    <a href="departments_simple.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>

                    <!-- البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2"
                                       placeholder="بحث بالاسم أو النوع..."
                                       value="<?php echo htmlspecialchars($search); ?>">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <?php if ($search): ?>
                                    <a href="departments_simple.php" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-times"></i>
                                    </a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>الوحدة الأب</th>
                                    <th>الوحدات الفرعية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($departments as $dept): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($dept['name']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $dept['type']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($dept['parent_name']): ?>
                                                <small class="text-muted"><?php echo $dept['parent_type']; ?>:</small><br>
                                                <?php echo htmlspecialchars($dept['parent_name']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">وحدة رئيسية</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $dept['child_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($dept['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="?edit=<?php echo $dept['id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="?action=delete&id=<?php echo $dept['id']; ?>" 
                                               class="btn btn-sm btn-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذه الوحدة؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
