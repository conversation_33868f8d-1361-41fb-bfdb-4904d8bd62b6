<?php
require_once 'config/config.php';
require_once 'config/database.php';

header('Content-Type: application/json; charset=utf-8');

$database = new Database();
$db = $database->getConnection();

try {
    // جلب جميع البيانات مع ترتيب حسب display_order
    $stmt = $db->query("SELECT 
        id, 
        name, 
        internal_number, 
        rank, 
        manager_name, 
        manager_rank, 
        internal_phone, 
        email, 
        description, 
        type, 
        parent_id, 
        display_order,
        is_active
        FROM departments_services_offices 
        WHERE is_active = 1 
        ORDER BY display_order ASC, type, name ASC");
    
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنظيف البيانات وإضافة معلومات إضافية
    $result = [];
    foreach ($departments as $dept) {
        $result[] = [
            'id' => (int)$dept['id'],
            'name' => $dept['name'],
            'internal_number' => $dept['internal_number'],
            'rank' => $dept['rank'],
            'manager_name' => $dept['manager_name'],
            'manager_rank' => $dept['manager_rank'],
            'internal_phone' => $dept['internal_phone'],
            'email' => $dept['email'],
            'description' => $dept['description'],
            'type' => $dept['type'],
            'parent_id' => $dept['parent_id'] ? (int)$dept['parent_id'] : null,
            'display_order' => (int)$dept['display_order'],
            'is_active' => (bool)$dept['is_active']
        ];
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
