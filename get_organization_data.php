<?php
require_once 'config/config.php';
require_once 'config/database.php';

header('Content-Type: application/json; charset=utf-8');

$database = new Database();
$db = $database->getConnection();

try {
    // التحقق من الأعمدة الموجودة
    $stmt_check = $db->query("SHOW COLUMNS FROM departments_services_offices");
    $existing_columns = [];
    while ($row = $stmt_check->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }

    $has_extended_fields = in_array('internal_number', $existing_columns);
    $has_display_order = in_array('display_order', $existing_columns);

    // بناء الاستعلام حسب الأعمدة المتاحة
    $select_fields = "id, name, type, parent_id, is_active";

    if ($has_extended_fields) {
        $select_fields .= ", internal_number, rank, manager_name, manager_rank, internal_phone, email, description";
    }

    if ($has_display_order) {
        $select_fields .= ", display_order";
    }

    $order_clause = $has_display_order ? "ORDER BY display_order ASC, type, name ASC" : "ORDER BY type, name ASC";

    $stmt = $db->query("SELECT $select_fields FROM departments_services_offices WHERE is_active = 1 $order_clause");

    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيف البيانات وإضافة معلومات إضافية
    $result = [];
    foreach ($departments as $dept) {
        $item = [
            'id' => (int)$dept['id'],
            'name' => $dept['name'],
            'type' => $dept['type'],
            'parent_id' => $dept['parent_id'] ? (int)$dept['parent_id'] : null,
            'is_active' => (bool)$dept['is_active']
        ];

        // إضافة الحقول الإضافية إذا كانت متاحة
        if ($has_extended_fields) {
            $item['internal_number'] = $dept['internal_number'] ?? null;
            $item['rank'] = $dept['rank'] ?? null;
            $item['manager_name'] = $dept['manager_name'] ?? null;
            $item['manager_rank'] = $dept['manager_rank'] ?? null;
            $item['internal_phone'] = $dept['internal_phone'] ?? null;
            $item['email'] = $dept['email'] ?? null;
            $item['description'] = $dept['description'] ?? null;
        }

        if ($has_display_order) {
            $item['display_order'] = (int)($dept['display_order'] ?? 0);
        }

        $result[] = $item;
    }

    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
