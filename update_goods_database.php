<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'تحديث قاعدة البيانات - النظام المتعدد المواد';

// التحقق من الصلاحيات (يمكن إضافة تحقق إضافي هنا)
$database = new Database();
$db = $database->getConnection();

$success_messages = [];
$error_messages = [];
$warnings = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_database'])) {
    try {
        // بدء المعاملة
        $db->beginTransaction();
        
        // 1. إنشاء جدول goods_entry_items
        $sql1 = "
        CREATE TABLE IF NOT EXISTS goods_entry_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            receipt_id INT NOT NULL,
            item_number INT NOT NULL,
            item_name VARCHAR(255) NOT NULL,
            item_code VARCHAR(100),
            inventory_number VARCHAR(100),
            quantity DECIMAL(10,2) DEFAULT 1.00,
            unit_price DECIMAL(15,2) NOT NULL,
            total_amount DECIMAL(15,2) NOT NULL,
            currency_type VARCHAR(10) NOT NULL DEFAULT 'DZD',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (receipt_id) REFERENCES goods_entry_receipts(id) ON DELETE CASCADE,
            INDEX idx_receipt_id (receipt_id),
            INDEX idx_item_number (item_number)
        )";
        
        $db->exec($sql1);
        $success_messages[] = "✅ تم إنشاء جدول goods_entry_items بنجاح";
        
        // 2. إضافة الأعمدة الجديدة
        $columns_to_add = [
            'total_items' => 'INT DEFAULT 0',
            'subtotal' => 'DECIMAL(15,2) DEFAULT 0.00',
            'tax_amount' => 'DECIMAL(15,2) DEFAULT 0.00',
            'grand_total' => 'DECIMAL(15,2) DEFAULT 0.00'
        ];
        
        foreach ($columns_to_add as $column => $definition) {
            try {
                $sql = "ALTER TABLE goods_entry_receipts ADD COLUMN $column $definition";
                $db->exec($sql);
                $success_messages[] = "✅ تم إضافة العمود $column بنجاح";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $warnings[] = "⚠️ العمود $column موجود مسبقاً";
                } else {
                    throw $e;
                }
            }
        }
        
        // 3. إنشاء الفهارس
        $indexes = [
            'idx_receipt_date' => 'receipt_date',
            'idx_receipt_number' => 'receipt_number',
            'idx_directorate_name' => 'directorate_name'
        ];
        
        foreach ($indexes as $index_name => $column) {
            try {
                $sql = "CREATE INDEX $index_name ON goods_entry_receipts($column)";
                $db->exec($sql);
                $success_messages[] = "✅ تم إنشاء الفهرس $index_name بنجاح";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                    $warnings[] = "⚠️ الفهرس $index_name موجود مسبقاً";
                } else {
                    throw $e;
                }
            }
        }
        
        // 4. إضافة القيود
        try {
            $constraints = [
                "ALTER TABLE goods_entry_items ADD CONSTRAINT chk_quantity_positive CHECK (quantity > 0)",
                "ALTER TABLE goods_entry_items ADD CONSTRAINT chk_unit_price_positive CHECK (unit_price >= 0)",
                "ALTER TABLE goods_entry_items ADD CONSTRAINT chk_total_amount_positive CHECK (total_amount >= 0)"
            ];
            
            foreach ($constraints as $constraint) {
                try {
                    $db->exec($constraint);
                } catch (PDOException $e) {
                    // تجاهل أخطاء القيود المكررة
                    if (strpos($e->getMessage(), 'Duplicate') === false) {
                        throw $e;
                    }
                }
            }
            $success_messages[] = "✅ تم إضافة القيود بنجاح";
        } catch (PDOException $e) {
            $warnings[] = "⚠️ بعض القيود موجودة مسبقاً";
        }
        
        // 5. تحديث البيانات الموجودة
        $sql_update = "
        UPDATE goods_entry_receipts 
        SET total_items = 1, 
            subtotal = COALESCE(total_amount, 0),
            grand_total = COALESCE(total_amount, 0)
        WHERE total_items = 0 AND total_amount IS NOT NULL
        ";
        
        $stmt = $db->prepare($sql_update);
        $stmt->execute();
        $affected_rows = $stmt->rowCount();
        
        if ($affected_rows > 0) {
            $success_messages[] = "✅ تم تحديث $affected_rows من الوصولات الموجودة";
        }
        
        // إتمام المعاملة
        $db->commit();
        
        $success_messages[] = "🎉 تم تحديث قاعدة البيانات بنجاح! يمكنك الآن استخدام النظام الجديد.";
        
    } catch (PDOException $e) {
        $db->rollBack();
        $error_messages[] = "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$database_status = [];

try {
    // فحص وجود جدول goods_entry_items
    $stmt = $db->query("SHOW TABLES LIKE 'goods_entry_items'");
    $database_status['goods_entry_items_exists'] = $stmt->rowCount() > 0;
    
    // فحص وجود الأعمدة الجديدة
    $stmt = $db->query("DESCRIBE goods_entry_receipts");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['total_items', 'subtotal', 'tax_amount', 'grand_total'];
    foreach ($required_columns as $column) {
        $database_status[$column . '_exists'] = in_array($column, $columns);
    }
    
} catch (PDOException $e) {
    $error_messages[] = "❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database me-2"></i>
                        تحديث قاعدة البيانات - النظام المتعدد المواد
                    </h3>
                </div>
                
                <div class="card-body">
                    <!-- عرض الرسائل -->
                    <?php if (!empty($success_messages)): ?>
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>رسائل النجاح:</h5>
                            <ul class="mb-0">
                                <?php foreach ($success_messages as $message): ?>
                                    <li><?php echo $message; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($warnings)): ?>
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات:</h5>
                            <ul class="mb-0">
                                <?php foreach ($warnings as $warning): ?>
                                    <li><?php echo $warning; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error_messages)): ?>
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                            <ul class="mb-0">
                                <?php foreach ($error_messages as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- حالة قاعدة البيانات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>حالة قاعدة البيانات الحالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>الجداول:</h6>
                                    <ul class="list-unstyled">
                                        <li>
                                            <i class="fas fa-<?php echo isset($database_status['goods_entry_items_exists']) && $database_status['goods_entry_items_exists'] ? 'check text-success' : 'times text-danger'; ?> me-2"></i>
                                            جدول goods_entry_items
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>الأعمدة الجديدة:</h6>
                                    <ul class="list-unstyled">
                                        <?php 
                                        $columns = ['total_items', 'subtotal', 'tax_amount', 'grand_total'];
                                        foreach ($columns as $column): 
                                        ?>
                                            <li>
                                                <i class="fas fa-<?php echo isset($database_status[$column . '_exists']) && $database_status[$column . '_exists'] ? 'check text-success' : 'times text-danger'; ?> me-2"></i>
                                                <?php echo $column; ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التحديث -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>ما سيتم تحديثه:</h5>
                        <ul>
                            <li><strong>إنشاء جدول جديد:</strong> goods_entry_items لحفظ المواد المتعددة</li>
                            <li><strong>إضافة أعمدة جديدة:</strong> total_items, subtotal, tax_amount, grand_total</li>
                            <li><strong>إنشاء فهارس:</strong> لتحسين أداء البحث</li>
                            <li><strong>إضافة قيود:</strong> للتأكد من صحة البيانات</li>
                            <li><strong>تحديث البيانات الموجودة:</strong> إذا كانت هناك وصولات سابقة</li>
                        </ul>
                    </div>

                    <!-- تحذير -->
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم:</h5>
                        <p><strong>يُنصح بعمل نسخة احتياطية من قاعدة البيانات قبل التحديث!</strong></p>
                        <p>هذا التحديث آمن ولن يؤثر على البيانات الموجودة، لكن من الأفضل الاحتياط.</p>
                    </div>

                    <!-- زر التحديث -->
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تحديث قاعدة البيانات؟')">
                        <div class="text-center">
                            <button type="submit" name="update_database" class="btn btn-primary btn-lg">
                                <i class="fas fa-database me-2"></i>
                                تحديث قاعدة البيانات
                            </button>
                        </div>
                    </form>

                    <!-- روابط مفيدة -->
                    <div class="mt-4 text-center">
                        <a href="goods_entry_multi.php" class="btn btn-success me-2">
                            <i class="fas fa-boxes me-2"></i>
                            النظام الجديد (متعدد المواد)
                        </a>
                        <a href="goods_entry.php" class="btn btn-secondary me-2">
                            <i class="fas fa-box me-2"></i>
                            النظام القديم (مادة واحدة)
                        </a>
                        <a href="database_update_guide.html" class="btn btn-info" target="_blank">
                            <i class="fas fa-book me-2"></i>
                            دليل التحديث
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
