<?php
if (!isset($db)) {
    require_once 'includes/config.php';
}

// الحصول على المعاملات
$section = isset($_GET['section']) ? $_GET['section'] : (isset($selected_section) ? $selected_section : 'bac_general');
$year = isset($_GET['year']) ? (int)$_GET['year'] : (isset($selected_year) ? $selected_year : date('Y'));

// تحديد الجدول والفئة
$table = '';
$category = '';
$exam_name = '';

switch ($section) {
    case 'bac_general':
        $table = 'excellent_students_bac';
        $category = 'general';
        $exam_name = 'البكالوريا - عام';
        break;
    case 'bac_military':
        $table = 'excellent_students_bac';
        $category = 'military_school';
        $exam_name = 'البكالوريا - أشبال الأمة';
        break;
    case 'bac_special':
        $table = 'excellent_students_bac';
        $category = 'special_needs';
        $exam_name = 'البكالوريا - ذوي الاحتياجات الخاصة';
        break;
    case 'bem_general':
        $table = 'excellent_students_bem';
        $category = 'general';
        $exam_name = 'التعليم المتوسط - عام';
        break;
    case 'bem_military':
        $table = 'excellent_students_bem';
        $category = 'military_school';
        $exam_name = 'التعليم المتوسط - أشبال الأمة';
        break;
    case 'bem_special':
        $table = 'excellent_students_bem';
        $category = 'special_needs';
        $exam_name = 'التعليم المتوسط - ذوي الاحتياجات الخاصة';
        break;
    default:
        $table = 'excellent_students_bac';
        $category = 'general';
        $exam_name = 'البكالوريا - عام';
}

try {
    // جلب البيانات
    $stmt = $db->prepare("SELECT * FROM $table WHERE category = ? AND exam_year = ? ORDER BY average DESC, last_name ASC");
    $stmt->execute([$category, $year]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo '<div class="data-table">';
    echo '<div class="table-responsive">';
    echo '<table class="table table-striped table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th style="width: 4%;">الرقم</th>';
    echo '<th style="width: 10%;">اللقب</th>';
    echo '<th style="width: 10%;">الاسم</th>';
    echo '<th style="width: 12%;">رقم بطاقة التعريف</th>';
    echo '<th style="width: 8%;">تاريخ الميلاد</th>';
    echo '<th style="width: 10%;">مكان الميلاد</th>';
    echo '<th style="width: 6%;">المعدل</th>';
    echo '<th style="width: 10%;">الشعبة</th>';
    echo '<th style="width: 8%;">الولاية</th>';
    echo '<th style="width: 12%;">التعيين</th>';
    echo '<th style="width: 6%;">إمضاء المعني</th>';
    echo '<th style="width: 6%;">البصمة</th>';
    echo '<th style="width: 8%;">العمليات</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    if (empty($students)) {
        echo '<tr>';
        echo '<td colspan="13" class="text-center py-4">';
        echo '<div class="alert alert-info mb-0">';
        echo '<i class="fas fa-info-circle"></i> ';
        echo 'لا توجد بيانات للعرض في قسم ' . $exam_name . ' لسنة ' . $year;
        echo '</div>';
        echo '</td>';
        echo '</tr>';
    } else {
        foreach ($students as $index => $student) {
            echo '<tr>';
            echo '<td><strong>' . ($index + 1) . '</strong></td>';
            echo '<td>' . htmlspecialchars($student['last_name']) . '</td>';
            echo '<td>' . htmlspecialchars($student['first_name']) . '</td>';

            // عمود رقم بطاقة التعريف مع خاصية النسخ
            echo '<td>';
            if (!empty($student['national_id'])) {
                echo '<div class="national-id-cell">';
                echo '<span class="national-id-text" id="national-id-' . $student['id'] . '">' . htmlspecialchars($student['national_id']) . '</span>';
                echo '<button class="btn btn-sm btn-outline-primary ms-1 copy-btn" onclick="copyNationalId(\'' . $student['id'] . '\')" title="نسخ رقم بطاقة التعريف">';
                echo '<i class="fas fa-copy"></i>';
                echo '</button>';
                echo '</div>';
            } else {
                echo '<span class="text-muted">غير محدد</span>';
            }
            echo '</td>';

            echo '<td>' . date('d/m/Y', strtotime($student['birth_date'])) . '</td>';
            echo '<td>' . htmlspecialchars($student['birth_place']) . '</td>';
            echo '<td><span class="badge bg-success">' . $student['average'] . '</span></td>';
            echo '<td>' . htmlspecialchars($student['branch']) . '</td>';
            echo '<td>' . htmlspecialchars($student['wilaya']) . '</td>';
            echo '<td>' . htmlspecialchars($student['assignment'] ?: '-') . '</td>';
            echo '<td>';
            if ($student['student_signature']) {
                echo '<i class="fas fa-check-circle text-success" title="تم التوقيع"></i>';
            } else {
                echo '<i class="fas fa-times-circle text-muted" title="لم يتم التوقيع"></i>';
            }
            echo '</td>';
            echo '<td>';
            if ($student['fingerprint']) {
                echo '<i class="fas fa-fingerprint text-primary" title="تم أخذ البصمة"></i>';
            } else {
                echo '<i class="fas fa-times-circle text-muted" title="لم يتم أخذ البصمة"></i>';
            }
            echo '</td>';
            echo '<td>';
            echo '<div class="btn-group btn-group-sm">';
            echo '<button class="btn btn-outline-primary btn-sm" onclick="editStudent(' . $student['id'] . ')" title="تعديل">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            echo '<button class="btn btn-outline-danger btn-sm" onclick="deleteStudent(' . $student['id'] . ')" title="حذف">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
    }

    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';

    // إحصائيات
    if (!empty($students)) {
        $total_students = count($students);
        $avg_score = array_sum(array_column($students, 'average')) / $total_students;
        $signed_count = count(array_filter($students, function($s) { return !empty($s['student_signature']); }));
        $fingerprint_count = count(array_filter($students, function($s) { return !empty($s['fingerprint']); }));

        echo '<div class="mt-3">';
        echo '<div class="row text-center">';
        echo '<div class="col-md-3">';
        echo '<div class="card bg-primary text-white">';
        echo '<div class="card-body">';
        echo '<h5>' . $total_students . '</h5>';
        echo '<small>إجمالي الطلاب</small>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '<div class="col-md-3">';
        echo '<div class="card bg-success text-white">';
        echo '<div class="card-body">';
        echo '<h5>' . number_format($avg_score, 2) . '</h5>';
        echo '<small>متوسط الدرجات</small>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '<div class="col-md-3">';
        echo '<div class="card bg-info text-white">';
        echo '<div class="card-body">';
        echo '<h5>' . $signed_count . '</h5>';
        echo '<small>التوقيعات المكتملة</small>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '<div class="col-md-3">';
        echo '<div class="card bg-warning text-white">';
        echo '<div class="card-body">';
        echo '<h5>' . $fingerprint_count . '</h5>';
        echo '<small>البصمات المكتملة</small>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

} catch (PDOException $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-triangle"></i> ';
    echo 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    echo '</div>';
}
?>

<script>
function editStudent(id) {
    window.location.href = `edit_excellent_student.php?id=${id}&section=${currentSection}&year=${currentYear}`;
}

function deleteStudent(id) {
    if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
        fetch('delete_excellent_student.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: id,
                section: currentSection
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف الطالب بنجاح');
                loadTableData();
            } else {
                alert('حدث خطأ في الحذف: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الحذف');
        });
    }
}
</script>
