<?php
require_once 'includes/config.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموحة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['id']) || !isset($input['section'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'بيانات غير مكتملة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$id = (int)$input['id'];
$section = $input['section'];

// تحديد الجدول
$table = '';
switch ($section) {
    case 'bac_general':
    case 'bac_military':
    case 'bac_special':
        $table = 'excellent_students_bac';
        break;
    case 'bem_general':
    case 'bem_military':
    case 'bem_special':
        $table = 'excellent_students_bem';
        break;
    default:
        echo json_encode([
            'success' => false,
            'message' => 'قسم غير صحيح'
        ], JSON_UNESCAPED_UNICODE);
        exit;
}

try {
    // التحقق من وجود الطالب
    $stmt = $db->prepare("SELECT last_name, first_name FROM $table WHERE id = ?");
    $stmt->execute([$id]);
    $student = $stmt->fetch();
    
    if (!$student) {
        echo json_encode([
            'success' => false,
            'message' => 'الطالب غير موجود'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // حذف الطالب
    $stmt = $db->prepare("DELETE FROM $table WHERE id = ?");
    $stmt->execute([$id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف الطالب: ' . $student['first_name'] . ' ' . $student['last_name']
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
