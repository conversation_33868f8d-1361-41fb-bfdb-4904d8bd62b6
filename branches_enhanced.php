<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'إدارة فروع الديوان الوطني - محسن';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// قائمة الولايات الجزائرية الـ 58
$available_branches = [
    'فرع أدرار', 'فرع الشلف', 'فرع الأغواط', 'فرع أم البواقي', 'فرع باتنة',
    'فرع بجاية', 'فرع بسكرة', 'فرع بشار', 'فرع البليدة', 'فرع البويرة',
    'فرع تمنراست', 'فرع تبسة', 'فرع تلمسان', 'فرع تيارت', 'فرع تيزي وزو',
    'فرع الجزائر', 'فرع الجلفة', 'فرع جيجل', 'فرع سطيف', 'فرع سعيدة',
    'فرع سكيكدة', 'فرع سيدي بلعباس', 'فرع عنابة', 'فرع قالمة', 'فرع قسنطينة',
    'فرع المدية', 'فرع مستغانم', 'فرع المسيلة', 'فرع معسكر', 'فرع ورقلة',
    'فرع وهران', 'فرع البيض', 'فرع إليزي', 'فرع برج بوعريريج', 'فرع بومرداس',
    'فرع الطارف', 'فرع تندوف', 'فرع تيسمسيلت', 'فرع الوادي', 'فرع خنشلة',
    'فرع سوق أهراس', 'فرع تيبازة', 'فرع ميلة', 'فرع عين الدفلى', 'فرع النعامة',
    'فرع عين تموشنت', 'فرع غرداية', 'فرع غليزان', 'فرع تيميمون', 'فرع برج باجي مختار',
    'فرع أولاد جلال', 'فرع بني عباس', 'فرع عين صالح', 'فرع عين قزام', 'فرع تقرت',
    'فرع جانت', 'فرع المغير', 'فرع المنيعة'
];

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $branch_name = sanitizeInput($_POST['branch_name']);
    $phone = sanitizeInput($_POST['phone']);
    $fax = sanitizeInput($_POST['fax']);
    $address = sanitizeInput($_POST['address']);
    $latitude = sanitizeInput($_POST['latitude'] ?? '');
    $longitude = sanitizeInput($_POST['longitude'] ?? '');
    $manager_name = sanitizeInput($_POST['manager_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $working_hours = sanitizeInput($_POST['working_hours'] ?? '');
    $services = sanitizeInput($_POST['services'] ?? '');
    $is_active = isset($_POST['is_active']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update']) || isset($_POST['action_type'])) {
            // التحقق من نوع العملية
            $is_update = (!empty($_POST['id']) && $_POST['id'] != '') ||
                        isset($_POST['update']) ||
                        (isset($_POST['action_type']) && $_POST['action_type'] == 'update');

            if ($is_update) {
                // تحديث فرع موجود
                $stmt = $db->prepare("UPDATE national_office_branches SET
                    branch_name = ?, phone = ?, fax = ?, address = ?, latitude = ?, longitude = ?,
                    manager_name = ?, email = ?, working_hours = ?, services = ?, is_active = ?
                    WHERE id = ?");

                $stmt->execute([$branch_name, $phone, $fax, $address, $latitude, $longitude,
                               $manager_name, $email, $working_hours, $services, $is_active, $_POST['id']]);

                $success_message = 'تم تحديث بيانات الفرع بنجاح';
            } else {
                // التحقق من عدم وجود الفرع مسبقاً
                $stmt = $db->prepare("SELECT COUNT(*) FROM national_office_branches WHERE branch_name = ?");
                $stmt->execute([$branch_name]);

                if ($stmt->fetchColumn() > 0) {
                    $error_message = 'هذا الفرع موجود بالفعل';
                } else {
                    // إضافة فرع جديد
                    $stmt = $db->prepare("INSERT INTO national_office_branches
                        (branch_name, phone, fax, address, latitude, longitude, manager_name, email, working_hours, services, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

                    $stmt->execute([$branch_name, $phone, $fax, $address, $latitude, $longitude,
                                   $manager_name, $email, $working_hours, $services, $is_active]);

                    $success_message = 'تم إضافة الفرع بنجاح';
                }
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        // التحقق من وجود تحويلات مرتبطة بالفرع
        $stmt = $db->prepare("SELECT COUNT(*) FROM transfer_vouchers WHERE branch_name = (SELECT branch_name FROM national_office_branches WHERE id = ?)");
        $stmt->execute([$id]);
        $transfer_count = $stmt->fetchColumn();
        
        if ($transfer_count > 0) {
            $error_message = 'لا يمكن حذف هذا الفرع لأنه مرتبط بسندات تحويل موجودة';
        } else {
            $stmt = $db->prepare("DELETE FROM national_office_branches WHERE id = ?");
            $stmt->execute([$id]);
            $success_message = 'تم حذف الفرع بنجاح';
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM national_office_branches WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE branch_name LIKE ? OR phone LIKE ? OR address LIKE ? OR manager_name LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT nob.*, 
                     (SELECT COUNT(*) FROM transfer_vouchers WHERE branch_name = nob.branch_name) as transfer_count
                     FROM national_office_branches nob 
                     $where_clause 
                     ORDER BY nob.branch_name ASC");
$stmt->execute($params);
$branches = $stmt->fetchAll();

include 'includes/header.php';
?>

<style>
    .branch-card {
        transition: transform 0.3s, box-shadow 0.3s;
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }
    .branch-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    .branch-header {
        background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
        color: white;
        padding: 20px;
        position: relative;
    }
    .branch-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
    }
    .map-container {
        height: 400px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .branch-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .status-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
    }
    .status-active {
        background: #28a745;
        color: white;
    }
    .status-inactive {
        background: #dc3545;
        color: white;
    }
    .add-branch-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        font-size: 24px;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        transition: all 0.3s;
        z-index: 1000;
    }
    .add-branch-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
    }
    .map-toggle-btn {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        margin: 5px;
        transition: all 0.3s;
    }
    .map-toggle-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
    }
    .map-toggle-btn.active {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
</style>

<div class="container-fluid py-4">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="branch-header text-center">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-building me-3"></i>
                    إدارة فروع الديوان الوطني للامتحانات والمسابقات
                </h1>
                <p class="lead mb-0">نظام إدارة شامل للفروع مع الخرائط التفاعلية</p>
            </div>
        </div>
    </div>

    <!-- رسائل النظام -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- شريط الأدوات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" onclick="showInteractiveMap()">
                    <i class="fas fa-map me-2"></i>خريطة تفاعلية
                </button>
                <button type="button" class="btn btn-info" onclick="showBranchStats()">
                    <i class="fas fa-chart-bar me-2"></i>إحصائيات
                </button>
                <button type="button" class="btn btn-success" onclick="exportBranches('excel')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
            </div>
        </div>
        <div class="col-md-6">
            <!-- البحث -->
            <form method="GET" class="d-flex">
                <input type="text" name="search" class="form-control me-2" 
                       placeholder="بحث بالاسم أو الهاتف أو العنوان أو المدير..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
                <?php if ($search): ?>
                    <a href="branches_enhanced.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times"></i>
                    </a>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white branch-card">
                <div class="card-body text-center">
                    <i class="fas fa-building fa-2x mb-2"></i>
                    <h4><?php echo count($branches); ?></h4>
                    <small>إجمالي الفروع</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white branch-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4><?php echo count(array_filter($branches, fn($b) => $b['is_active'])); ?></h4>
                    <small>فروع نشطة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white branch-card">
                <div class="card-body text-center">
                    <i class="fas fa-pause-circle fa-2x mb-2"></i>
                    <h4><?php echo count(array_filter($branches, fn($b) => !$b['is_active'])); ?></h4>
                    <small>فروع معطلة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white branch-card">
                <div class="card-body text-center">
                    <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                    <h4><?php echo count(array_filter($branches, fn($b) => !empty($b['latitude']) && !empty($b['longitude']))); ?></h4>
                    <small>فروع بإحداثيات</small>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الفروع -->
    <div class="row">
        <?php foreach ($branches as $branch): ?>
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card branch-card h-100">
                    <div class="card-header bg-gradient-primary text-white position-relative">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building me-2"></i>
                            <?php echo htmlspecialchars($branch['branch_name']); ?>
                        </h5>
                        <span class="status-badge <?php echo $branch['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                            <?php echo $branch['is_active'] ? 'نشط' : 'معطل'; ?>
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="branch-info-card">
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <strong>العنوان:</strong> <?php echo htmlspecialchars($branch['address']); ?>
                            </p>
                            
                            <?php if (!empty($branch['manager_name'])): ?>
                                <p class="mb-2">
                                    <i class="fas fa-user-tie text-success me-2"></i>
                                    <strong>المدير:</strong> <?php echo htmlspecialchars($branch['manager_name']); ?>
                                </p>
                            <?php endif; ?>
                            
                            <?php if (!empty($branch['phone'])): ?>
                                <p class="mb-2">
                                    <i class="fas fa-phone text-info me-2"></i>
                                    <strong>الهاتف:</strong> <?php echo htmlspecialchars($branch['phone']); ?>
                                </p>
                            <?php endif; ?>
                            
                            <?php if (!empty($branch['email'])): ?>
                                <p class="mb-2">
                                    <i class="fas fa-envelope text-warning me-2"></i>
                                    <strong>البريد:</strong> <?php echo htmlspecialchars($branch['email']); ?>
                                </p>
                            <?php endif; ?>
                            
                            <?php if (!empty($branch['working_hours'])): ?>
                                <p class="mb-2">
                                    <i class="fas fa-clock text-secondary me-2"></i>
                                    <strong>ساعات العمل:</strong> <?php echo htmlspecialchars($branch['working_hours']); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-exchange-alt me-1"></i>
                                <?php echo $branch['transfer_count']; ?> تحويل
                            </small>
                            
                            <?php if (!empty($branch['latitude']) && !empty($branch['longitude'])): ?>
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="showBranchOnMap(<?php echo $branch['latitude']; ?>, <?php echo $branch['longitude']; ?>, '<?php echo htmlspecialchars($branch['branch_name']); ?>')">
                                    <i class="fas fa-map-marker-alt me-1"></i>على الخريطة
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-primary btn-sm"
                                    onclick="editBranch(<?php echo $branch['id']; ?>)"
                                    title="تعديل بيانات الفرع">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-outline-info btn-sm"
                                    onclick="viewBranchDetails(<?php echo $branch['id']; ?>)"
                                    title="عرض تفاصيل الفرع">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-outline-danger btn-sm"
                                    onclick="deleteBranch(<?php echo $branch['id']; ?>)"
                                    title="حذف الفرع">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">ID: <?php echo $branch['id']; ?></small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- زر إضافة فرع جديد -->
    <button class="add-branch-btn" onclick="showAddBranchModal()" title="إضافة فرع جديد">
        <i class="fas fa-plus"></i>
    </button>
</div>

<!-- نموذج إضافة/تعديل فرع -->
<div class="modal fade" id="branchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-building me-2"></i>
                    <span id="modalTitle">إضافة فرع جديد</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="branchForm">
                <div class="modal-body">
                    <input type="hidden" name="id" id="branchId">
                    <input type="hidden" name="action_type" id="actionType" value="save">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الفرع <span class="text-danger">*</span></label>
                                <select class="form-select" name="branch_name" id="branchName" required>
                                    <option value="">اختر الفرع</option>
                                    <?php foreach ($available_branches as $branch): ?>
                                        <option value="<?php echo $branch; ?>"><?php echo $branch; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المدير</label>
                                <input type="text" class="form-control" name="manager_name" id="managerName"
                                       placeholder="اسم مدير الفرع">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" name="phone" id="phone"
                                       placeholder="رقم الهاتف">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الفاكس</label>
                                <input type="text" class="form-control" name="fax" id="fax"
                                       placeholder="رقم الفاكس">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email" id="email"
                               placeholder="البريد الإلكتروني">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">العنوان <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="address" id="address" rows="2"
                                  placeholder="العنوان الكامل للفرع" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">خط العرض (Latitude)</label>
                                <input type="number" step="any" class="form-control" name="latitude" id="latitude"
                                       placeholder="36.7538">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">خط الطول (Longitude)</label>
                                <input type="number" step="any" class="form-control" name="longitude" id="longitude"
                                       placeholder="3.0588">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="getCurrentLocation()">
                            <i class="fas fa-map-marker-alt me-2"></i>تحديد الموقع الحالي
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="showLocationPicker()">
                            <i class="fas fa-map me-2"></i>اختيار من الخريطة
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ساعات العمل</label>
                        <input type="text" class="form-control" name="working_hours" id="workingHours"
                               placeholder="الأحد - الخميس: 8:00 - 16:30">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الخدمات المتاحة</label>
                        <textarea class="form-control" name="services" id="services" rows="3"
                                  placeholder="التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات"></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" id="isActive" checked>
                        <label class="form-check-label" for="isActive">
                            فرع نشط
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" name="save" class="btn btn-primary" id="saveBtn">
                        <i class="fas fa-plus me-2"></i>إضافة الفرع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج الخريطة التفاعلية -->
<div class="modal fade" id="mapModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-map me-2"></i>
                    خريطة فروع الديوان الوطني
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="d-flex justify-content-center p-3 bg-light">
                    <button class="map-toggle-btn active" onclick="switchMap('google')" id="googleBtn">
                        <i class="fab fa-google me-2"></i>Google Maps
                    </button>
                    <button class="map-toggle-btn" onclick="switchMap('yandex')" id="yandexBtn">
                        <i class="fas fa-map me-2"></i>Yandex Maps
                    </button>
                    <button class="map-toggle-btn" onclick="switchMap('osm')" id="osmBtn">
                        <i class="fas fa-globe me-2"></i>OpenStreetMap
                    </button>
                </div>
                <div id="mapContainer" class="map-container" style="height: 500px;">
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري تحميل الخريطة...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحميل مكتبة Leaflet -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// بيانات الفروع للخريطة
const branchesData = <?php echo json_encode($branches, JSON_UNESCAPED_UNICODE); ?>;

// عرض نموذج إضافة فرع جديد
function showAddBranchModal() {
    // إعادة تعيين النموذج
    document.getElementById('branchForm').reset();

    // تحديث العنوان
    document.getElementById('modalTitle').textContent = 'إضافة فرع جديد';

    // تنظيف الحقول المخفية
    document.getElementById('branchId').value = '';
    document.getElementById('actionType').value = 'save';

    // تحديث زر الحفظ
    const saveBtn = document.getElementById('saveBtn');
    saveBtn.innerHTML = '<i class="fas fa-plus me-2"></i>إضافة الفرع';
    saveBtn.name = 'save';
    saveBtn.className = 'btn btn-primary';

    // تعيين القيم الافتراضية
    document.getElementById('isActive').checked = true;

    // عرض النافذة
    new bootstrap.Modal(document.getElementById('branchModal')).show();
}

// عرض نموذج تعديل فرع
function editBranch(id) {
    console.log('تم استدعاء editBranch مع ID:', id);
    console.log('بيانات الفروع المتاحة:', branchesData);

    // التأكد من وجود البيانات
    if (!branchesData || branchesData.length === 0) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ في البيانات',
            text: 'لا توجد بيانات فروع متاحة. يرجى تحديث الصفحة.'
        });
        return;
    }

    const branch = branchesData.find(b => b.id == id);
    console.log('الفرع الموجود:', branch);

    if (branch) {
        try {
            // تحديث عنوان النافذة
            document.getElementById('modalTitle').textContent = 'تعديل بيانات الفرع';

            // ملء البيانات
            document.getElementById('branchId').value = branch.id;
            document.getElementById('actionType').value = 'update';

            // ملء الحقول مع التحقق من وجودها
            const branchNameSelect = document.getElementById('branchName');
            if (branchNameSelect) {
                branchNameSelect.value = branch.branch_name || '';
            }

            const fields = [
                { id: 'managerName', value: branch.manager_name },
                { id: 'phone', value: branch.phone },
                { id: 'fax', value: branch.fax },
                { id: 'email', value: branch.email },
                { id: 'address', value: branch.address },
                { id: 'latitude', value: branch.latitude },
                { id: 'longitude', value: branch.longitude },
                { id: 'workingHours', value: branch.working_hours },
                { id: 'services', value: branch.services }
            ];

            fields.forEach(field => {
                const element = document.getElementById(field.id);
                if (element) {
                    element.value = field.value || '';
                } else {
                    console.warn(`العنصر ${field.id} غير موجود`);
                }
            });

            // حالة النشاط
            const isActiveCheckbox = document.getElementById('isActive');
            if (isActiveCheckbox) {
                isActiveCheckbox.checked = branch.is_active == 1;
            }

            // تحديث زر الحفظ
            const saveBtn = document.getElementById('saveBtn');
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="fas fa-edit me-2"></i>تحديث البيانات';
                saveBtn.name = 'update';
                saveBtn.className = 'btn btn-warning';
            }

            // عرض النافذة
            const modal = new bootstrap.Modal(document.getElementById('branchModal'));
            modal.show();

            console.log('تم فتح نموذج التعديل بنجاح');

        } catch (error) {
            console.error('خطأ في ملء النموذج:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ في النموذج',
                text: 'حدث خطأ أثناء ملء بيانات التعديل: ' + error.message
            });
        }
    } else {
        console.error('لم يتم العثور على الفرع مع ID:', id);
        Swal.fire({
            icon: 'error',
            title: 'فرع غير موجود',
            text: `لم يتم العثور على الفرع رقم ${id}. يرجى تحديث الصفحة والمحاولة مرة أخرى.`
        });
    }
}

// حذف فرع
function deleteBranch(id) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا الفرع؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// عرض تفاصيل الفرع
function viewBranchDetails(id) {
    const branch = branchesData.find(b => b.id == id);
    if (branch) {
        const detailsHtml = `
            <div class="branch-details">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات أساسية</h6>
                        <p><strong>اسم الفرع:</strong> ${branch.branch_name}</p>
                        <p><strong>المدير:</strong> ${branch.manager_name || 'غير محدد'}</p>
                        <p><strong>الحالة:</strong> <span class="badge ${branch.is_active ? 'bg-success' : 'bg-danger'}">${branch.is_active ? 'نشط' : 'معطل'}</span></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">معلومات الاتصال</h6>
                        <p><strong>الهاتف:</strong> ${branch.phone || 'غير محدد'}</p>
                        <p><strong>الفاكس:</strong> ${branch.fax || 'غير محدد'}</p>
                        <p><strong>البريد:</strong> ${branch.email || 'غير محدد'}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-success">العنوان والموقع</h6>
                        <p><strong>العنوان:</strong> ${branch.address}</p>
                        ${branch.latitude && branch.longitude ?
                            `<p><strong>الإحداثيات:</strong> ${branch.latitude}, ${branch.longitude}</p>` :
                            '<p class="text-muted">لا توجد إحداثيات محددة</p>'
                        }
                    </div>
                </div>
                ${branch.working_hours ? `
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-warning">ساعات العمل</h6>
                            <p>${branch.working_hours}</p>
                        </div>
                    </div>
                ` : ''}
                ${branch.services ? `
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-secondary">الخدمات المتاحة</h6>
                            <p>${branch.services}</p>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        Swal.fire({
            title: `تفاصيل ${branch.branch_name}`,
            html: detailsHtml,
            width: '600px',
            confirmButtonText: 'إغلاق',
            customClass: {
                htmlContainer: 'text-start'
            }
        });
    }
}

// تحديد الموقع الحالي
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            document.getElementById('latitude').value = position.coords.latitude.toFixed(6);
            document.getElementById('longitude').value = position.coords.longitude.toFixed(6);
            Swal.fire({
                icon: 'success',
                title: 'تم تحديد الموقع',
                text: 'تم تحديد موقعك الحالي بنجاح',
                timer: 2000,
                showConfirmButton: false
            });
        }, function(error) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ في تحديد الموقع',
                text: 'لم يتم السماح بالوصول للموقع أو حدث خطأ'
            });
        });
    } else {
        Swal.fire({
            icon: 'error',
            title: 'غير مدعوم',
            text: 'متصفحك لا يدعم تحديد الموقع الجغرافي'
        });
    }
}

// عرض الخريطة التفاعلية
function showInteractiveMap() {
    new bootstrap.Modal(document.getElementById('mapModal')).show();
    setTimeout(() => {
        initializeMap();
    }, 500);
}

// تبديل نوع الخريطة
function switchMap(type) {
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.map-toggle-btn').forEach(btn => btn.classList.remove('active'));

    // إضافة الفئة النشطة للزر المحدد
    document.getElementById(type + 'Btn').classList.add('active');

    // تحديث الخريطة
    initializeMap(type);
}

// تهيئة الخريطة
function initializeMap(mapType = 'google') {
    const mapContainer = document.getElementById('mapContainer');

    if (mapType === 'google') {
        initGoogleMap(mapContainer);
    } else if (mapType === 'yandex') {
        initYandexMap(mapContainer);
    } else if (mapType === 'osm') {
        initOpenStreetMap(mapContainer);
    }
}

// تهيئة خريطة Google
function initGoogleMap(container) {
    container.innerHTML = `
        <div class="text-center p-4">
            <h5 class="text-primary mb-3">خريطة Google Maps</h5>
            <p class="text-muted">يتطلب مفتاح API من Google للعرض الكامل</p>
            <div class="alert alert-info">
                <strong>الفروع المتاحة:</strong><br>
                ${branchesData.map(branch =>
                    `<span class="badge bg-primary me-2 mb-1">${branch.branch_name}</span>`
                ).join('')}
            </div>
            <button class="btn btn-primary" onclick="openGoogleMapsExternal()">
                <i class="fab fa-google me-2"></i>فتح في Google Maps
            </button>
        </div>
    `;
}

// تهيئة خريطة Yandex
function initYandexMap(container) {
    container.innerHTML = `
        <iframe
            src="https://yandex.com/map-widget/v1/?um=constructor%3A36.7538%2C3.0588%2C6z&amp;source=constructor"
            width="100%"
            height="500"
            frameborder="0"
            style="border-radius: 10px;">
        </iframe>
    `;
}

// تهيئة خريطة OpenStreetMap
function initOpenStreetMap(container) {
    container.innerHTML = `<div id="osmMap" style="height: 500px; border-radius: 10px;"></div>`;

    // تهيئة خريطة Leaflet
    setTimeout(() => {
        try {
            if (typeof L !== 'undefined' && document.getElementById('osmMap')) {
                const map = L.map('osmMap').setView([36.7538, 3.0588], 6);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);

                // إضافة علامات للفروع
                branchesData.forEach(branch => {
                    if (branch.latitude && branch.longitude) {
                        const marker = L.marker([parseFloat(branch.latitude), parseFloat(branch.longitude)])
                            .addTo(map);

                        const popupContent = `
                            <div class="text-center">
                                <h6 class="text-primary">${branch.branch_name}</h6>
                                <p class="mb-1"><small>${branch.address}</small></p>
                                ${branch.manager_name ? `<p class="mb-1"><strong>المدير:</strong> ${branch.manager_name}</p>` : ''}
                                ${branch.phone ? `<p class="mb-0"><strong>الهاتف:</strong> ${branch.phone}</p>` : ''}
                            </div>
                        `;

                        marker.bindPopup(popupContent);
                    }
                });

                // إضافة تحكم في الطبقات
                const baseMaps = {
                    "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'),
                    "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}')
                };

                L.control.layers(baseMaps).addTo(map);

            } else {
                throw new Error('Leaflet library not loaded or map container not found');
            }
        } catch (error) {
            console.error('Error initializing OpenStreetMap:', error);
            container.innerHTML = `
                <div class="alert alert-warning text-center">
                    <h5>خطأ في تحميل الخريطة</h5>
                    <p>لم يتم تحميل مكتبة Leaflet بشكل صحيح</p>
                    <button class="btn btn-primary" onclick="initOpenStreetMap(document.getElementById('mapContainer'))">
                        <i class="fas fa-redo me-2"></i>إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }, 1500);
}

// عرض فرع على الخريطة
function showBranchOnMap(lat, lng, name) {
    const mapUrl = `https://www.google.com/maps?q=${lat},${lng}&z=15&t=m`;
    window.open(mapUrl, '_blank');
}

// فتح Google Maps خارجياً
function openGoogleMapsExternal() {
    const center = "36.7538,3.0588";
    const url = `https://www.google.com/maps/@${center},6z`;
    window.open(url, '_blank');
}

// عرض إحصائيات الفروع
function showBranchStats() {
    const activeCount = branchesData.filter(b => b.is_active == 1).length;
    const inactiveCount = branchesData.filter(b => b.is_active == 0).length;
    const withCoordinates = branchesData.filter(b => b.latitude && b.longitude).length;
    const withEmail = branchesData.filter(b => b.email && b.email.trim() !== '').length;
    const withManager = branchesData.filter(b => b.manager_name && b.manager_name.trim() !== '').length;

    const statsHtml = `
        <div class="stats-container">
            <div class="row text-center mb-4">
                <div class="col-md-3">
                    <div class="stat-card bg-primary text-white p-3 rounded mb-3">
                        <i class="fas fa-building fa-2x mb-2"></i>
                        <h3>${branchesData.length}</h3>
                        <p class="mb-0">إجمالي الفروع</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card bg-success text-white p-3 rounded mb-3">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3>${activeCount}</h3>
                        <p class="mb-0">فروع نشطة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card bg-warning text-white p-3 rounded mb-3">
                        <i class="fas fa-pause-circle fa-2x mb-2"></i>
                        <h3>${inactiveCount}</h3>
                        <p class="mb-0">فروع معطلة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card bg-info text-white p-3 rounded mb-3">
                        <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                        <h3>${withCoordinates}</h3>
                        <p class="mb-0">فروع بإحداثيات</p>
                    </div>
                </div>
            </div>

            <div class="row text-center">
                <div class="col-md-6">
                    <div class="stat-card bg-secondary text-white p-3 rounded">
                        <i class="fas fa-envelope fa-2x mb-2"></i>
                        <h4>${withEmail}</h4>
                        <p class="mb-0">فروع ببريد إلكتروني</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stat-card bg-dark text-white p-3 rounded">
                        <i class="fas fa-user-tie fa-2x mb-2"></i>
                        <h4>${withManager}</h4>
                        <p class="mb-0">فروع بمدير محدد</p>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <h6 class="text-center text-muted">نسبة الاكتمال</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: ${(withCoordinates/branchesData.length*100).toFixed(1)}%">
                        إحداثيات: ${(withCoordinates/branchesData.length*100).toFixed(1)}%
                    </div>
                </div>
                <div class="progress mb-2">
                    <div class="progress-bar bg-info" style="width: ${(withEmail/branchesData.length*100).toFixed(1)}%">
                        بريد إلكتروني: ${(withEmail/branchesData.length*100).toFixed(1)}%
                    </div>
                </div>
                <div class="progress">
                    <div class="progress-bar bg-warning" style="width: ${(withManager/branchesData.length*100).toFixed(1)}%">
                        مديرين: ${(withManager/branchesData.length*100).toFixed(1)}%
                    </div>
                </div>
            </div>
        </div>
    `;

    Swal.fire({
        title: 'إحصائيات شاملة للفروع',
        html: statsHtml,
        width: '800px',
        confirmButtonText: 'إغلاق',
        customClass: {
            htmlContainer: 'text-start'
        }
    });
}

// عرض منتقي الموقع
function showLocationPicker() {
    Swal.fire({
        title: 'اختيار الموقع من الخريطة',
        html: `
            <div id="locationPickerMap" style="height: 400px; border-radius: 10px;"></div>
            <div class="mt-3">
                <div class="row">
                    <div class="col-6">
                        <label class="form-label">خط العرض:</label>
                        <input type="number" id="pickedLat" class="form-control" step="any" readonly>
                    </div>
                    <div class="col-6">
                        <label class="form-label">خط الطول:</label>
                        <input type="number" id="pickedLng" class="form-control" step="any" readonly>
                    </div>
                </div>
            </div>
        `,
        width: '600px',
        showCancelButton: true,
        confirmButtonText: 'استخدام هذا الموقع',
        cancelButtonText: 'إلغاء',
        didOpen: () => {
            if (typeof L !== 'undefined') {
                const map = L.map('locationPickerMap').setView([36.7538, 3.0588], 6);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

                let marker = null;

                map.on('click', function(e) {
                    if (marker) {
                        map.removeLayer(marker);
                    }
                    marker = L.marker(e.latlng).addTo(map);
                    document.getElementById('pickedLat').value = e.latlng.lat.toFixed(6);
                    document.getElementById('pickedLng').value = e.latlng.lng.toFixed(6);
                });
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const lat = document.getElementById('pickedLat').value;
            const lng = document.getElementById('pickedLng').value;
            if (lat && lng) {
                document.getElementById('latitude').value = lat;
                document.getElementById('longitude').value = lng;
                Swal.fire({
                    icon: 'success',
                    title: 'تم تحديد الموقع',
                    text: `الإحداثيات: ${lat}, ${lng}`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        }
    });
}

// تصدير قائمة الفروع
function exportBranches(format) {
    // إنشاء البيانات للتصدير
    const exportData = branchesData.map(branch => ({
        'اسم الفرع': branch.branch_name,
        'المدير': branch.manager_name || '',
        'الهاتف': branch.phone || '',
        'الفاكس': branch.fax || '',
        'البريد الإلكتروني': branch.email || '',
        'العنوان': branch.address,
        'خط العرض': branch.latitude || '',
        'خط الطول': branch.longitude || '',
        'ساعات العمل': branch.working_hours || '',
        'الخدمات': branch.services || '',
        'الحالة': branch.is_active ? 'نشط' : 'معطل'
    }));

    if (format === 'excel') {
        // تحويل إلى CSV للتصدير
        const csvContent = convertToCSV(exportData);
        downloadCSV(csvContent, 'فروع_الديوان_الوطني.csv');
    } else if (format === 'json') {
        const jsonContent = JSON.stringify(exportData, null, 2);
        downloadJSON(jsonContent, 'فروع_الديوان_الوطني.json');
    }
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [];

    // إضافة الرؤوس
    csvRows.push(headers.join(','));

    // إضافة البيانات
    for (const row of data) {
        const values = headers.map(header => {
            const value = row[header];
            return `"${value}"`;
        });
        csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
}

// تحميل ملف CSV
function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحميل ملف JSON
function downloadJSON(content, filename) {
    const blob = new Blob([content], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث النموذج عند تغيير اسم الفرع
document.addEventListener('DOMContentLoaded', function() {
    const branchNameSelect = document.getElementById('branchName');
    if (branchNameSelect) {
        branchNameSelect.addEventListener('change', function() {
            const selectedBranch = this.value;
            // يمكن إضافة منطق لملء البيانات التلقائية حسب الفرع المختار
            if (selectedBranch) {
                // مثال: ملء ساعات العمل الافتراضية
                const workingHoursField = document.getElementById('workingHours');
                if (workingHoursField && !workingHoursField.value) {
                    workingHoursField.value = 'الأحد - الخميس: 8:00 - 16:30';
                }

                // مثال: ملء الخدمات الافتراضية
                const servicesField = document.getElementById('services');
                if (servicesField && !servicesField.value) {
                    servicesField.value = 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات';
                }
            }
        });
    }

    // التأكد من ظهور زر الإضافة
    const addBtn = document.querySelector('.add-branch-btn');
    if (addBtn) {
        console.log('زر الإضافة موجود ومرئي');
    } else {
        console.error('زر الإضافة غير موجود!');
    }

    // معالجة إرسال النموذج
    const branchForm = document.getElementById('branchForm');
    if (branchForm) {
        branchForm.addEventListener('submit', function(e) {
            const branchName = document.getElementById('branchName').value;
            const address = document.getElementById('address').value;

            if (!branchName || !address) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'بيانات ناقصة',
                    text: 'يرجى ملء اسم الفرع والعنوان على الأقل'
                });
                return false;
            }

            // إظهار رسالة تحميل
            Swal.fire({
                title: 'جاري الحفظ...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
