<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تحديث قاعدة البيانات - خطوة بخطوة</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .guide-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .guide-title {
            background: #007bff;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .step-title {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            margin: -15px -15px 10px -15px;
            border-radius: 5px 5px 0 0;
            font-weight: bold;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .screenshot {
            border: 2px solid #007bff;
            border-radius: 5px;
            padding: 10px;
            background: #f8f9fa;
            margin: 10px 0;
            text-align: center;
        }
        
        .method {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .method-title {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            margin: -15px -15px 10px -15px;
            border-radius: 5px 5px 0 0;
            font-weight: bold;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>

<div class="guide-container">
    <div class="guide-title">🗄️ دليل تحديث قاعدة البيانات - خطوة بخطوة</div>
    
    <div class="warning">
        <h4>⚠️ تحذير مهم:</h4>
        <p><strong>قم بعمل نسخة احتياطية من قاعدة البيانات قبل التحديث!</strong></p>
        <p>هذا التحديث سيضيف جداول وأعمدة جديدة لدعم النظام المتعدد المواد.</p>
    </div>

    <h3>📋 ما سيتم تحديثه:</h3>
    <div class="highlight">
        <ul>
            <li>✅ <strong>إنشاء جدول جديد:</strong> goods_entry_items (لحفظ المواد المتعددة)</li>
            <li>✅ <strong>إضافة أعمدة جديدة:</strong> في جدول goods_entry_receipts</li>
            <li>✅ <strong>إنشاء فهارس:</strong> لتحسين الأداء</li>
            <li>✅ <strong>إضافة قيود:</strong> للتأكد من صحة البيانات</li>
        </ul>
    </div>

    <h3>🔧 طرق تحديث قاعدة البيانات:</h3>

    <!-- الطريقة الأولى: phpMyAdmin -->
    <div class="method">
        <div class="method-title">الطريقة الأولى: استخدام phpMyAdmin (الأسهل)</div>
        
        <div class="step">
            <div class="step-title">الخطوة 1: فتح phpMyAdmin</div>
            <p>افتح المتصفح واذهب إلى:</p>
            <div class="code-block">http://localhost/phpmyadmin</div>
            <p>أو</p>
            <div class="code-block">http://127.0.0.1/phpmyadmin</div>
        </div>

        <div class="step">
            <div class="step-title">الخطوة 2: اختيار قاعدة البيانات</div>
            <p>من القائمة اليسرى، انقر على اسم قاعدة البيانات الخاصة بك (مثل: <strong>sirius18</strong>)</p>
            <div class="screenshot">
                <p>📸 ستجد قائمة بالجداول مثل: suppliers, goods_entry_receipts, currencies, إلخ</p>
            </div>
        </div>

        <div class="step">
            <div class="step-title">الخطوة 3: فتح تبويب SQL</div>
            <p>انقر على تبويب <strong>"SQL"</strong> في الأعلى</p>
            <div class="screenshot">
                <p>📸 ستظهر منطقة نص كبيرة لكتابة أوامر SQL</p>
            </div>
        </div>

        <div class="step">
            <div class="step-title">الخطوة 4: نسخ ولصق الكود</div>
            <p>انسخ الكود التالي والصقه في منطقة النص:</p>
            <div class="code-block">
-- إنشاء جدول لمواد وصل دخول السلع
CREATE TABLE IF NOT EXISTS goods_entry_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_id INT NOT NULL,
    item_number INT NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    item_code VARCHAR(100),
    inventory_number VARCHAR(100),
    quantity DECIMAL(10,2) DEFAULT 1.00,
    unit_price DECIMAL(15,2) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    currency_type VARCHAR(10) NOT NULL DEFAULT 'DZD',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (receipt_id) REFERENCES goods_entry_receipts(id) ON DELETE CASCADE,
    INDEX idx_receipt_id (receipt_id),
    INDEX idx_item_number (item_number)
);

-- إضافة أعمدة جديدة للمجاميع
ALTER TABLE goods_entry_receipts 
ADD COLUMN IF NOT EXISTS total_items INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS subtotal DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS grand_total DECIMAL(15,2) DEFAULT 0.00;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_receipt_date ON goods_entry_receipts(receipt_date);
CREATE INDEX IF NOT EXISTS idx_receipt_number ON goods_entry_receipts(receipt_number);
CREATE INDEX IF NOT EXISTS idx_directorate_name ON goods_entry_receipts(directorate_name);
            </div>
        </div>

        <div class="step">
            <div class="step-title">الخطوة 5: تنفيذ الكود</div>
            <p>انقر على زر <strong>"تنفيذ"</strong> أو <strong>"Go"</strong></p>
            <div class="success">
                <p>✅ إذا ظهرت رسالة نجاح خضراء، فقد تم التحديث بنجاح!</p>
            </div>
        </div>
    </div>

    <!-- الطريقة الثانية: ملف SQL -->
    <div class="method">
        <div class="method-title">الطريقة الثانية: استخدام ملف SQL</div>
        
        <div class="step">
            <div class="step-title">الخطوة 1: فتح ملف SQL</div>
            <p>افتح الملف: <strong>create_goods_items_table.sql</strong> في محرر النصوص</p>
        </div>

        <div class="step">
            <div class="step-title">الخطوة 2: نسخ المحتوى</div>
            <p>انسخ كامل محتوى الملف</p>
        </div>

        <div class="step">
            <div class="step-title">الخطوة 3: تنفيذ في phpMyAdmin</div>
            <p>اتبع نفس خطوات الطريقة الأولى (الخطوات 1-5)</p>
        </div>
    </div>

    <!-- الطريقة الثالثة: سطر الأوامر -->
    <div class="method">
        <div class="method-title">الطريقة الثالثة: سطر الأوامر (للمتقدمين)</div>
        
        <div class="step">
            <div class="step-title">فتح Command Prompt</div>
            <p>افتح موجه الأوامر وانتقل إلى مجلد المشروع:</p>
            <div class="code-block">
cd C:\xampp\htdocs\Sirius18
mysql -u root -p sirius18 < create_goods_items_table.sql
            </div>
        </div>
    </div>

    <h3>✅ التحقق من نجاح التحديث:</h3>
    
    <div class="step">
        <div class="step-title">الخطوة 1: فحص الجداول</div>
        <p>في phpMyAdmin، تحقق من وجود:</p>
        <ul>
            <li>✅ <strong>جدول جديد:</strong> goods_entry_items</li>
            <li>✅ <strong>أعمدة جديدة في goods_entry_receipts:</strong> total_items, subtotal, tax_amount, grand_total</li>
        </ul>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 2: اختبار النظام</div>
        <p>افتح النظام الجديد:</p>
        <div class="code-block">http://localhost/Sirius18/goods_entry_multi.php</div>
        <p>إذا فتح بدون أخطاء، فالتحديث نجح!</p>
    </div>

    <h3>🚨 حل المشاكل الشائعة:</h3>

    <div class="warning">
        <h4>❌ خطأ: Table 'goods_entry_receipts' doesn't exist</h4>
        <p><strong>الحل:</strong> تأكد من أن قاعدة البيانات تحتوي على الجداول الأساسية أولاً</p>
    </div>

    <div class="warning">
        <h4>❌ خطأ: Column already exists</h4>
        <p><strong>الحل:</strong> هذا طبيعي، الكود يتحقق من وجود الأعمدة قبل إضافتها</p>
    </div>

    <div class="warning">
        <h4>❌ خطأ: Access denied</h4>
        <p><strong>الحل:</strong> تأكد من صحة اسم المستخدم وكلمة المرور لقاعدة البيانات</p>
    </div>

    <h3>📋 نسخة احتياطية (مهم جداً):</h3>
    
    <div class="step">
        <div class="step-title">قبل التحديث - عمل نسخة احتياطية</div>
        <p>في phpMyAdmin:</p>
        <ol>
            <li>اختر قاعدة البيانات</li>
            <li>انقر على تبويب <strong>"تصدير"</strong> أو <strong>"Export"</strong></li>
            <li>انقر <strong>"تنفيذ"</strong> أو <strong>"Go"</strong></li>
            <li>احفظ الملف في مكان آمن</li>
        </ol>
    </div>

    <h3>🎯 خطوات سريعة (ملخص):</h3>
    
    <div class="success">
        <h4>للمبتدئين - الطريقة السريعة:</h4>
        <ol>
            <li>افتح: <code>http://localhost/phpmyadmin</code></li>
            <li>اختر قاعدة البيانات (sirius18)</li>
            <li>انقر تبويب "SQL"</li>
            <li>انسخ والصق الكود من الأعلى</li>
            <li>انقر "تنفيذ"</li>
            <li>تحقق من ظهور رسالة النجاح</li>
            <li>اختبر النظام: <code>goods_entry_multi.php</code></li>
        </ol>
    </div>

    <h3>📞 إذا واجهت مشاكل:</h3>
    
    <div class="highlight">
        <p>إذا واجهت أي مشاكل في التحديث:</p>
        <ul>
            <li>🔹 تأكد من تشغيل XAMPP/WAMP</li>
            <li>🔹 تأكد من صحة اسم قاعدة البيانات</li>
            <li>🔹 جرب إعادة تشغيل Apache و MySQL</li>
            <li>🔹 تحقق من ملف config/database.php</li>
        </ul>
    </div>

    <div class="success">
        <h4>✅ بعد التحديث الناجح:</h4>
        <p>ستتمكن من:</p>
        <ul>
            <li>✅ إضافة عدة مواد في نفس الوصل</li>
            <li>✅ ترقيم تلقائي للمواد</li>
            <li>✅ حسابات تلقائية للمجاميع</li>
            <li>✅ طباعة جدول احترافي</li>
            <li>✅ إدارة متكاملة للوصولات</li>
        </ul>
    </div>
</div>

</body>
</html>
