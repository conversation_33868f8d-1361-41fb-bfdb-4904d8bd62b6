# تحسينات نظام المديريات والمصالح

## نظرة عامة
تم تطوير نظام إدارة المديريات والمصالح في Sirius18 بإضافة ميزات إدارية متقدمة وهيكل تنظيمي تفاعلي.

## الميزات الجديدة المضافة

### 1. الحقول الإدارية الجديدة
- **الرقم الداخلي**: رقم تعريف داخلي لكل مديرية/مصلحة
- **الرتبة**: رتبة الوحدة الإدارية (مدير، رئيس مصلحة، إلخ)
- **اسم المسؤول**: اسم الشخص المسؤول عن الوحدة
- **رتبة المسؤول**: الرتبة الأكاديمية أو المهنية للمسؤول
- **الهاتف الداخلي**: رقم الهاتف الداخلي للتواصل
- **البريد الإلكتروني**: عنوان البريد الإلكتروني الرسمي
- **الوصف**: وصف تفصيلي للوحدة الإدارية
- **ترتيب العرض**: ترقيم لترتيب عرض الوحدات

### 2. الهيكل التنظيمي التفاعلي
- **عرض هرمي**: عرض الهيكل التنظيمي في شكل هرمي تفاعلي
- **توسيع/طي العقد**: إمكانية توسيع أو طي الأقسام الفرعية
- **معلومات مفصلة**: عرض جميع المعلومات الإدارية لكل وحدة
- **تعديل مباشر**: إمكانية التعديل المباشر من الهيكل التنظيمي
- **طباعة وتصدير**: إمكانية طباعة الهيكل أو تصديره

### 3. الإحصائيات المحسنة
- **إحصائيات تفصيلية**: عدد الوحدات النشطة والإجمالية لكل نوع
- **إحصائيات المسؤولين**: عدد الوحدات التي لديها مسؤولين معينين
- **ألوان مميزة**: ألوان مختلفة لكل نوع من أنواع الوحدات
- **أيقونات تعبيرية**: أيقونات مميزة لكل نوع

### 4. تحسينات البحث
- **بحث شامل**: البحث في جميع الحقول الجديدة
- **بحث متقدم**: البحث بالاسم، النوع، الرقم الداخلي، الرتبة، والمسؤول

### 5. تحسينات واجهة المستخدم
- **قوائم منسدلة**: قوائم محددة مسبقاً للرتب والرتب الأكاديمية
- **التحقق من صحة البيانات**: تحقق من صحة البريد الإلكتروني والهاتف
- **عرض محسن للجدول**: عرض أفضل للمعلومات الجديدة في الجدول
- **ألوان وأيقونات**: استخدام ألوان وأيقونات مميزة للمعلومات

## الملفات المضافة/المحدثة

### الملفات الجديدة
1. `database/update_departments_table.sql` - سكريبت تحديث قاعدة البيانات
2. `update_departments_database.php` - ملف تنفيذ تحديث قاعدة البيانات
3. `get_organization_data.php` - API لجلب بيانات الهيكل التنظيمي
4. `toggle_department_status.php` - API لتغيير حالة الوحدات الإدارية

### الملفات المحدثة
1. `departments.php` - الملف الرئيسي مع جميع التحسينات

## قاعدة البيانات

### الأعمدة الجديدة في جدول `departments_services_offices`
```sql
- internal_number VARCHAR(50) - الرقم الداخلي
- rank VARCHAR(100) - الرتبة
- manager_name VARCHAR(255) - اسم المسؤول
- manager_rank VARCHAR(100) - رتبة المسؤول
- internal_phone VARCHAR(20) - الهاتف الداخلي
- email VARCHAR(255) - البريد الإلكتروني
- description TEXT - الوصف
- display_order INT DEFAULT 0 - ترتيب العرض
```

### الفهارس المضافة
- فهرس على `internal_number` لتسريع البحث
- فهرس على `rank` لتسريع التصفية
- فهرس على `display_order` لتسريع الترتيب

## كيفية الاستخدام

### 1. تحديث قاعدة البيانات
```bash
# تشغيل سكريبت التحديث
php update_departments_database.php
```

### 2. إضافة وحدة إدارية جديدة
1. املأ جميع الحقول المطلوبة
2. اختر الرتبة من القائمة المنسدلة
3. أدخل معلومات المسؤول إذا كانت متوفرة
4. احفظ البيانات

### 3. عرض الهيكل التنظيمي
1. انقر على زر "الهيكل التنظيمي"
2. استخدم أزرار التوسيع/الطي حسب الحاجة
3. انقر على "تعديل" لتعديل أي وحدة
4. استخدم أزرار الطباعة والتصدير

### 4. البحث والتصفية
- استخدم مربع البحث للبحث في جميع الحقول
- البحث يشمل: الاسم، النوع، الرقم الداخلي، الرتبة، اسم المسؤول

## الميزات التقنية

### الأمان
- استخدام Prepared Statements لمنع SQL Injection
- تنظيف جميع المدخلات باستخدام `sanitizeInput()`
- التحقق من صحة البيانات على مستوى العميل والخادم

### الأداء
- فهارس محسنة لتسريع البحث والترتيب
- استخدام AJAX لتحديث الحالة دون إعادة تحميل الصفحة
- تحميل البيانات بشكل ديناميكي للهيكل التنظيمي

### التوافق
- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية واتجاه RTL

## المتطلبات التقنية
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Bootstrap 5.3
- Font Awesome 6.0
- SweetAlert2

## الدعم والصيانة
- جميع الميزات مختبرة ومتوافقة مع النظام الحالي
- إمكانية التوسع لإضافة ميزات جديدة
- كود منظم وموثق للصيانة السهلة
