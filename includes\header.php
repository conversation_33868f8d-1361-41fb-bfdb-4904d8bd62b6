<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Print CSS -->
    <link href="assets/css/print.css" rel="stylesheet" media="print">
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        
        .institution-header {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .institution-header h1 {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            text-align: center;
        }
        
        .institution-header h2 {
            font-size: 1.4rem;
            font-weight: 500;
            margin: 5px 0;
            text-align: center;
        }
        
        .institution-header h3 {
            font-size: 1.2rem;
            font-weight: 400;
            margin: 5px 0;
            text-align: center;
        }
        
        .navbar {
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c5530 !important;
        }
        
        .nav-link {
            color: #495057 !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: #2c5530 !important;
        }
        
        .nav-link.active {
            color: #2c5530 !important;
            font-weight: 600;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            transition: transform 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #2c5530;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ced4da;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #2c5530;
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
        }
        
        .badge {
            border-radius: 6px;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }

            .institution-header {
                background: none !important;
                color: black !important;
                border-bottom: 2px solid black;
            }

            /* تعيين اتجاه الطباعة للممونين كعمودي (Portrait) */
            @page {
                size: A4 portrait;
                margin: 15mm;
            }

            /* تحسين عرض جدول الممونين للطباعة العمودية */
            .table {
                font-size: 10px;
                width: 100%;
            }

            .table th,
            .table td {
                padding: 4px 2px;
                font-size: 9px;
                word-wrap: break-word;
                max-width: 80px;
            }

            /* إخفاء الأعمدة غير الضرورية في الطباعة */
            .table .no-print-column {
                display: none !important;
            }

            /* تحسين عرض النص الطويل */
            .text-truncate-print {
                white-space: normal !important;
                overflow: visible !important;
                text-overflow: clip !important;
            }
        }

        /* Custom Styles for Warehouse Supplies */
        .btn-outline-purple {
            color: #6f42c1;
            border-color: #6f42c1;
        }
        .btn-outline-purple:hover {
            color: #fff;
            background-color: #6f42c1;
            border-color: #6f42c1;
        }
        .bg-purple {
            background-color: #6f42c1 !important;
        }
        .text-purple {
            color: #6f42c1 !important;
        }
    </style>
</head>
<body>
    <!-- رأس المؤسسة -->
    <div class="institution-header no-print">
        <div class="container">
            <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
            <h2><?php echo MINISTRY_NAME_AR; ?></h2>
            <h3><?php echo OFFICE_NAME_AR; ?></h3>
        </div>
    </div>

    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-light no-print">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-warehouse me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes me-1"></i>
                            إدارة المخزون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="inventory_cards.php">بطاقة المخزون</a></li>
                            <li><a class="dropdown-item" href="goods_entry.php">وصل دخول السلع</a></li>
                            <li><a class="dropdown-item" href="office_supplies.php">طلب اللوازم المكتبية</a></li>
                            <li><a class="dropdown-item" href="transfer_vouchers.php">سند التحويل</a></li>
                            <li><a class="dropdown-item" href="general_inventory.php">الجرد العام</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="excellent_students.php">
                                <i class="fas fa-trophy me-2"></i>جدول التلاميذ المتفوقين في الامتحانات المدرسية
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.php">
                            <i class="fas fa-truck me-1"></i>
                            الممونين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="consultations.php">
                            <i class="fas fa-file-contract me-1"></i>
                            الاستشارات
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="reports/stock_report.php">تقرير المخزون</a></li>
                            <li><a class="dropdown-item" href="reports/low_stock_alert.php">تنبيهات المخزون المنخفض</a></li>
                            <li><a class="dropdown-item" href="reports/monthly_report.php">التقرير الشهري</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="settingsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">المديريات والمصالح</a></li>
                            <li><a class="dropdown-item" href="branches.php">الفروع</a></li>
                            <li><a class="dropdown-item" href="settings/backup.php">النسخ الاحتياطي</a></li>
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo formatArabicDate(date('Y-m-d')); ?>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid">
        <?php
        // عرض رسائل النجاح أو الخطأ
        if (isset($_SESSION['success_message'])) {
            echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            echo '<i class="fas fa-check-circle me-2"></i>' . $_SESSION['success_message'];
            echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            echo '</div>';
            unset($_SESSION['success_message']);
        }
        
        if (isset($_SESSION['error_message'])) {
            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            echo '<i class="fas fa-exclamation-circle me-2"></i>' . $_SESSION['error_message'];
            echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            echo '</div>';
            unset($_SESSION['error_message']);
        }
        ?>
