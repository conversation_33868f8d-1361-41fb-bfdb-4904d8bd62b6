<?php
echo "<h2>اختبار بيانات الهيكل التنظيمي</h2>";

try {
    require_once 'includes/config.php';
    
    // التحقق من الأعمدة الموجودة
    $stmt_check = $db->query("SHOW COLUMNS FROM departments_services_offices");
    $existing_columns = [];
    while ($row = $stmt_check->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }
    
    echo "<h3>الأعمدة المتاحة:</h3>";
    echo "<ul>";
    foreach ($existing_columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    $has_extended_fields = in_array('internal_number', $existing_columns);
    $has_display_order = in_array('display_order', $existing_columns);
    
    echo "<p><strong>الحقول الموسعة متاحة:</strong> " . ($has_extended_fields ? 'نعم' : 'لا') . "</p>";
    echo "<p><strong>عمود ترتيب العرض متاح:</strong> " . ($has_display_order ? 'نعم' : 'لا') . "</p>";
    
    // بناء الاستعلام حسب الأعمدة المتاحة
    $select_fields = "id, name, type, parent_id, is_active";
    
    if ($has_extended_fields) {
        $select_fields .= ", internal_number, rank, manager_name, manager_rank, internal_phone, email, description";
    }
    
    if ($has_display_order) {
        $select_fields .= ", display_order";
    }
    
    $order_clause = $has_display_order ? "ORDER BY display_order ASC, type, name ASC" : "ORDER BY type, name ASC";
    
    echo "<h3>الاستعلام المستخدم:</h3>";
    echo "<code>SELECT $select_fields FROM departments_services_offices WHERE is_active = 1 $order_clause</code>";
    
    $stmt = $db->query("SELECT $select_fields FROM departments_services_offices WHERE is_active = 1 $order_clause");
    
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>النتائج:</h3>";
    echo "<p><strong>عدد السجلات:</strong> " . count($departments) . "</p>";
    
    if (count($departments) > 0) {
        echo "<h4>البيانات:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach (array_keys($departments[0]) as $header) {
            echo "<th style='padding: 5px; background: #f0f0f0;'>$header</th>";
        }
        echo "</tr>";
        
        foreach ($departments as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td style='padding: 5px;'>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>JSON Output:</h4>";
        
        // تنظيف البيانات وإضافة معلومات إضافية
        $result = [];
        foreach ($departments as $dept) {
            $item = [
                'id' => (int)$dept['id'],
                'name' => $dept['name'],
                'type' => $dept['type'],
                'parent_id' => $dept['parent_id'] ? (int)$dept['parent_id'] : null,
                'is_active' => (bool)$dept['is_active']
            ];
            
            // إضافة الحقول الإضافية إذا كانت متاحة
            if ($has_extended_fields) {
                $item['internal_number'] = $dept['internal_number'] ?? null;
                $item['rank'] = $dept['rank'] ?? null;
                $item['manager_name'] = $dept['manager_name'] ?? null;
                $item['manager_rank'] = $dept['manager_rank'] ?? null;
                $item['internal_phone'] = $dept['internal_phone'] ?? null;
                $item['email'] = $dept['email'] ?? null;
                $item['description'] = $dept['description'] ?? null;
            }
            
            if ($has_display_order) {
                $item['display_order'] = (int)($dept['display_order'] ?? 0);
            }
            
            $result[] = $item;
        }
        
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
        echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        echo "</pre>";
        
    } else {
        echo "<p style='color: orange;'>لا توجد بيانات نشطة</p>";
        
        // فحص جميع البيانات
        $stmt_all = $db->query("SELECT COUNT(*) as total FROM departments_services_offices");
        $total = $stmt_all->fetch()['total'];
        echo "<p>إجمالي السجلات (بما في ذلك غير النشطة): $total</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
ul { padding-right: 20px; }
li { margin: 5px 0; }
code { background: #f0f0f0; padding: 2px 5px; border-radius: 3px; }
</style>
