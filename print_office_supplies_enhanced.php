<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? '14';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// أحجام الخط المتاحة
$available_sizes = ['12', '14', '16', '18', '20'];
if (!in_array($font_size, $available_sizes)) {
    $font_size = '14';
}

// حساب الأحجام المختلفة
$base_size = $font_size . 'px';
$header_size = ($font_size + 4) . 'px';
$title_size = ($font_size + 6) . 'px';
$table_size = ($font_size - 1) . 'px';
$small_size = ($font_size - 2) . 'px';

// خيارات التنسيق
$bold_text = $request['bold_text'] ?? false;
$text_align = $request['text_align'] ?? 'right';
$font_color = $request['font_color'] ?? 'black';

// تحويل الألوان
$color_map = [
    'black' => '#000000',
    'blue' => '#0066cc',
    'red' => '#cc0000',
    'green' => '#006600',
    'brown' => '#8B4513',
    'purple' => '#800080'
];
$text_color = $color_map[$font_color] ?? '#000000';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة طلب اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: right 5px
            margin: left 45px;
            direction: rtl;
            font-size: <?php echo $base_size; ?>;
            line-height: 1.3;
            color: <?php echo $text_color; ?>;
            <?php if ($bold_text): ?>
            font-weight: bold;
            <?php endif; ?>
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
        }
        .header h1 { 
            color: #2c5530; 
            margin: 5px 0; 
            font-size: <?php echo $header_size; ?>;
        }
        .header h2 { 
            color: #4a7c59; 
            margin: 5px 0; 
            font-size: <?php echo $base_size; ?>;
        }
        .header h3 { 
            color: #666; 
            margin: 5px 0; 
            font-size: <?php echo $small_size; ?>;
        }
        .form-title {
            text-align: center;
            font-size: <?php echo $title_size; ?>;
            font-weight: bold;
            margin: 10px 0;
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 8px;
            background-color: #f8f9fa;
        }
        .info-section {
            margin: 8px 0;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .info-row {
            display: flex;
            margin: 4px 0;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            width: 140px;
            color: #2c5530;
            font-size: <?php echo $small_size; ?>;
        }
        .info-value {
            flex: 1;
            padding: 3px;
            border-bottom: 1px solid #ddd;
            font-size: <?php echo $small_size; ?>;
        }
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 8px 0;
        }
        .materials-table th,
        .materials-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: center;
            font-size: <?php echo $small_size; ?>;
            vertical-align: middle;
        }
        .materials-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .materials-table td.item-name {
            text-align: <?php echo $text_align; ?>;
            <?php if ($bold_text): ?>
            font-weight: bold;
            <?php else: ?>
            font-weight: normal;
            <?php endif; ?>
        }
        .materials-table td.notes {
            text-align: <?php echo $text_align; ?>;
            font-size: <?php echo $small_size; ?>;
        }
        .signatures-section {
            margin-top: 20px;
            page-break-inside: avoid;
        }
        .signatures-title {
            text-align: center;
            font-size: <?php echo $small_size; ?>;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c5530;
        }
        .signature-row {
            display: grid;
            grid-template-columns: 8% 45% 12% 18% 17%;
            margin-bottom: 50px;
            padding: 20px 0;
            gap: 0;
        }
        .signature-box {
            border: none;
            padding: 15px 3px;
            text-align: center;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: <?php echo ($font_size - 2) . 'px'; ?>;
            font-weight: bold;
            color: #000;
            background-color: transparent;
            margin: 8px 1px;
            white-space: normal;
            overflow: visible;
            word-wrap: break-word;
            line-height: 1.3;
        }
        .signature-box.span-2 {
            grid-column: span 2;
        }
        .signature-box.span-3 {
            grid-column: span 3;
        }
        @media print {
            body {
                margin: 2mm !important;
                padding: 0 !important;
                font-size: <?php echo ($font_size - 2) . 'px'; ?>;
                line-height: 1.1;
            }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
            .header {
                margin-bottom: 3px;
                padding-bottom: 2px;
                margin-top: 0;
            }
            .header h1, .header h2, .header h3 {
                margin: 1px 0 !important;
                padding: 0 !important;
            }
            .form-title { margin: 2px 0; padding: 2px; }
            .info-section {
                margin: 2px 0;
                padding: 2px;
                border: none;
            }
            .materials-table {
                margin: 3px 0 !important;
                border-collapse: collapse;
            }
            .materials-table th, .materials-table td {
                padding: 1px 2px;
                font-size: <?php echo ($font_size - 3) . 'px'; ?>;
                border: 1px solid #000;
            }
            .signature-box {
                padding: 6px 3px;
                min-height: 30px;
                font-size: <?php echo ($font_size - 2) . 'px'; ?>;
                font-weight: bold !important;
                color: #000 !important;
                margin: 3px 0;
                white-space: normal !important;
                overflow: visible !important;
                word-wrap: break-word !important;
                line-height: 1.2 !important;
                text-align: center !important;
                border: none !important;
            }
            .signatures-section { margin-top: 8px; }
            .signature-row {
                margin-bottom: 10px;
                padding: 3px 0;
            }
            /* تقليل المسافة فوق جدول المعلومات */
            table[style*="border-collapse"] {
                margin-top: 0 !important;
                margin-bottom: 0 !important;
            }
        }
        .footer {
            margin-top: 15px;
            text-align: center;
            border-top: 1px solid #ccc;
            padding-top: 8px;
            font-size: <?php echo ($font_size - 4) . 'px'; ?>;
            color: #666;
        }
        .font-size-selector {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- اختيار حجم الخط -->
    <div class="font-size-selector no-print">
        <label for="fontSizeSelect" class="form-label">حجم الخط:</label>
        <select id="fontSizeSelect" class="form-select form-select-sm" onchange="changeFontSize(this.value)">
            <option value="12" <?php echo $font_size == '12' ? 'selected' : ''; ?>>12px</option>
            <option value="14" <?php echo $font_size == '14' ? 'selected' : ''; ?>>14px</option>
            <option value="16" <?php echo $font_size == '16' ? 'selected' : ''; ?>>16px</option>
            <option value="18" <?php echo $font_size == '18' ? 'selected' : ''; ?>>18px</option>
            <option value="20" <?php echo $font_size == '20' ? 'selected' : ''; ?>>20px</option>
        </select>
    </div>

    <div class="header">
        <h1 style="font-weight: bold; color: #000; font-size: <?php echo ($font_size + 2) . 'px'; ?>; text-shadow: 0.5px 0.5px 0px #333;">الجمهورية الجزائرية الديمقراطية الشعبية</h1>
        <h2 style="font-weight: bold; color: #000; font-size: <?php echo ($font_size + 1) . 'px'; ?>; text-shadow: 0.5px 0.5px 0px #333;">وزارة التربية الوطنية</h2>
        <h3 style="font-weight: bold; color: #000; font-size: <?php echo $font_size . 'px'; ?>; text-shadow: 0.5px 0.5px 0px #333;">الديوان الوطني للامتحانات والمسابقات</h3>
    </div>


    


    <!-- معلومات فوق الجدول: رقم الوصل والمديرية (يسار) + التاريخ والمستلم (يمين) -->
    <table style="width: 100%; border-collapse: collapse; margin: 10px 0; table-layout: fixed;">
        <tr>
            <!-- معلومات اليسار: رقم الوصل والمديرية -->
            <td style="width: 50%; text-align: right; padding: 5px; font-size: <?php echo ($font_size - 1) . 'px'; ?>; font-weight: bold; vertical-align: top;">
                رقم الوصل: <?php echo htmlspecialchars($request['receipt_number']); ?><br>
                المديرية أو المصلحة المستفيدة: <?php echo htmlspecialchars($request['beneficiary_directorate']); ?>
            </td>
            <!-- معلومات اليمين: التاريخ والمستلم -->
            <td style="width: 50%; text-align: left; padding: 5px; font-size: <?php echo ($font_size - 1) . 'px'; ?>; font-weight: bold; vertical-align: top;">
                التاريخ: <?php echo formatArabicDate($request['request_date']); ?><br>
                المستلم: <?php echo htmlspecialchars($request['recipient_name']); ?>
            </td>
        </tr>
    </table>

    <!-- جدول المواد -->
    <table class="materials-table">
        <thead>
            <tr>
                <th width="8%">الرقم</th>
                <th width="45%">اسم المادة</th>
                <th width="12%">الكمية</th>
                <th width="18%">رقم اللوازم</th>
                <th width="17%">الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($materials)): ?>
                <tr>
                    <td colspan="5">لا توجد مواد مسجلة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($materials as $index => $material): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td class="item-name">
                            <?php echo htmlspecialchars($material['item_name']); ?>
                        </td>
                        <td>
                            <strong><?php echo number_format($material['quantity'], 0); ?></strong>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($material['supply_number'] ?: '-'); ?>
                        </td>
                        <td class="notes">
                            <?php echo htmlspecialchars($material['notes'] ?? '-'); ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <!-- الملاحظات العامة -->
    <?php if ($request['notes']): ?>
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">الملاحظات العامة:</div>
                <div class="info-value"><?php echo nl2br(htmlspecialchars($request['notes'])); ?></div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- التوقيعات - تظهر دائماً -->
    <div class="signatures-section">

        <!-- الصف الأول: رئيس مصلحة الوسائل + المدير الفرعي -->
        <div class="signature-row">
            <!-- رئيس مصلحة الوسائل (العمود الأول - الرقم) -->
            <div class="signature-box">
                إمضاء وختم رئيس مصلحة الوسائل
            </div>
            <!-- أعمدة فارغة (اسم المادة + الكمية + رقم اللوازم) -->
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <!-- المدير الفرعي (العمود الأخير - الملاحظات) -->
            <div class="signature-box">
                إمضاء وختم المدير الفرعي للإدارة العامة
            </div>
        </div>

        <!-- مسافة بين الصفوف -->
        <div style="height: 40px; margin: 25px 0;"></div>

        <!-- الصف الثاني: المكلف بتسيير المخزن + المستفيد -->
        <div class="signature-row">
            <!-- المكلف بتسيير المخزن (العمود الأول - الرقم) -->
            <div class="signature-box">
                إمضاء وختم المكلف بتسيير المخزن
            </div>
            <!-- أعمدة فارغة (اسم المادة + الكمية + رقم اللوازم) -->
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <div class="signature-box" style="border: none;"></div>
            <!-- المستفيد (العمود الأخير - الملاحظات) -->
            <div class="signature-box">
                إمضاء وختم المستفيد
            </div>
        </div>
    </div>
    

    
    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
        <a href="office_supplies.php" class="btn btn-info btn-lg">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <script>
        // تغيير حجم الخط
        function changeFontSize(size) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('font_size', size);
            window.location.href = currentUrl.toString();
        }
    </script>
</body>
</html>
