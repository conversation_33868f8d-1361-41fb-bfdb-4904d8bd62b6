<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'وصل دخول السلع إلى المخزن - متعدد المواد';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// جلب قائمة الممونين
$stmt = $db->query("SELECT id, full_name FROM suppliers ORDER BY full_name ASC");
$suppliers = $stmt->fetchAll();

// جلب قائمة العملات
$stmt = $db->query("SELECT code, name_ar, symbol FROM currencies WHERE is_active = 1 ORDER BY name_ar ASC");
$currencies = $stmt->fetchAll();

// بيانات التعديل
$edit_data = [];
$edit_items = [];

if ($action === 'edit' && $id) {
    $stmt = $db->prepare("SELECT * FROM goods_entry_receipts WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
    
    if ($edit_data) {
        // جلب المواد
        $stmt = $db->prepare("SELECT * FROM goods_entry_items WHERE receipt_id = ? ORDER BY item_number ASC");
        $stmt->execute([$id]);
        $edit_items = $stmt->fetchAll();
    }
}

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $receipt_number = sanitizeInput($_POST['receipt_number']);
    $directorate_name = sanitizeInput($_POST['directorate_name']);
    $supplier_id = !empty($_POST['supplier_id']) ? intval($_POST['supplier_id']) : null;
    $receipt_date = $_POST['receipt_date'];
    $currency_type = 'DZD'; // تعيين العملة افتراضياً إلى الدينار الجزائري
    
    // التوقيعات
    $signature_warehouse_manager = isset($_POST['signature_warehouse_manager']);
    $signature_means_chief = isset($_POST['signature_means_chief']);
    $signature_sub_director = isset($_POST['signature_sub_director']);
    
    // معالجة المواد
    $items = [];
    $total_items = 0;
    $subtotal = 0;
    
    if (isset($_POST['items']) && is_array($_POST['items'])) {
        foreach ($_POST['items'] as $item) {
            if (!empty($item['item_name']) && !empty($item['unit_price'])) {
                $quantity = floatval($item['quantity'] ?? 0) ?: 1;
                $unit_price = floatval($item['unit_price']);
                $total_amount = $quantity * $unit_price;
                
                $items[] = [
                    'item_name' => sanitizeInput($item['item_name'] ?? ''),
                    'item_code' => sanitizeInput($item['item_code'] ?? ''),
                    'inventory_number' => sanitizeInput($item['inventory_number'] ?? ''),
                    'quantity' => $quantity,
                    'unit_price' => $unit_price,
                    'total_amount' => $total_amount,
                    'notes' => sanitizeInput($item['notes'] ?? '')
                ];
                
                $total_items++;
                $subtotal += $total_amount;
            }
        }
    }
    
    $tax_amount = 0; // يمكن إضافة حساب الضريبة لاحقاً
    $grand_total = $subtotal + $tax_amount;
    
    try {
        $db->beginTransaction();
        
        if (isset($_POST['save'])) {
            // إدراج وصل جديد
            $stmt = $db->prepare("
                INSERT INTO goods_entry_receipts 
                (receipt_number, directorate_name, supplier_id, receipt_date, currency_type, 
                 total_items, subtotal, tax_amount, grand_total,
                 signature_warehouse_manager, signature_means_chief, signature_sub_director) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $receipt_number, $directorate_name, $supplier_id, $receipt_date, $currency_type,
                $total_items, $subtotal, $tax_amount, $grand_total,
                $signature_warehouse_manager, $signature_means_chief, $signature_sub_director
            ]);
            
            $receipt_id = $db->lastInsertId();
            
        } elseif (isset($_POST['update']) && $id) {
            // تحديث وصل موجود
            $stmt = $db->prepare("
                UPDATE goods_entry_receipts 
                SET receipt_number = ?, directorate_name = ?, supplier_id = ?, receipt_date = ?, 
                    currency_type = ?, total_items = ?, subtotal = ?, tax_amount = ?, grand_total = ?,
                    signature_warehouse_manager = ?, signature_means_chief = ?, signature_sub_director = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $receipt_number, $directorate_name, $supplier_id, $receipt_date, $currency_type,
                $total_items, $subtotal, $tax_amount, $grand_total,
                $signature_warehouse_manager, $signature_means_chief, $signature_sub_director, $id
            ]);
            
            $receipt_id = $id;
            
            // حذف المواد القديمة
            $stmt = $db->prepare("DELETE FROM goods_entry_items WHERE receipt_id = ?");
            $stmt->execute([$receipt_id]);
        }
        
        // إدراج المواد
        if (!empty($items)) {
            $stmt = $db->prepare("
                INSERT INTO goods_entry_items 
                (receipt_id, item_number, item_name, item_code, inventory_number, quantity, unit_price, total_amount, currency_type, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $item_number = 1;
            foreach ($items as $item) {
                $stmt->execute([
                    $receipt_id, $item_number, $item['item_name'], $item['item_code'], 
                    $item['inventory_number'], $item['quantity'], $item['unit_price'], 
                    $item['total_amount'], $currency_type, $item['notes']
                ]);
                $item_number++;
            }
        }
        
        $db->commit();
        
        if (isset($_POST['save'])) {
            $success_message = 'تم حفظ وصل دخول السلع بنجاح';
        } else {
            $success_message = 'تم تحديث وصل دخول السلع بنجاح';
        }
        
        // إعادة تعيين البيانات
        $edit_data = [];
        $edit_items = [];
        
    } catch (PDOException $e) {
        $db->rollBack();
        $error_message = 'خطأ في حفظ البيانات: ' . $e->getMessage();
    }
}

// حذف وصل
if ($action === 'delete' && $id) {
    try {
        $stmt = $db->prepare("DELETE FROM goods_entry_receipts WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف الوصل بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف الوصل: ' . $e->getMessage();
    }
}

// جلب قائمة الوصولات
$where_clause = '';
$params = [];

if ($search) {
    $where_clause = "WHERE ger.receipt_number LIKE ? OR ger.directorate_name LIKE ? OR s.full_name LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param, $search_param];
}

$stmt = $db->prepare("
    SELECT ger.*, s.full_name as supplier_name
    FROM goods_entry_receipts ger
    LEFT JOIN suppliers s ON ger.supplier_id = s.id
    $where_clause
    ORDER BY ger.created_at DESC
");

$stmt->execute($params);
$receipts = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-boxes me-2"></i>
                        <?php echo $page_title; ?>
                    </h3>
                </div>
                
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- نموذج إدخال البيانات -->
                    <form method="POST" class="needs-validation" novalidate id="goodsEntryForm">
                        <?php if ($action === 'edit' && $id): ?>
                            <input type="hidden" name="update" value="1">
                        <?php else: ?>
                            <input type="hidden" name="save" value="1">
                        <?php endif; ?>

                        <div class="row">
                            <!-- معلومات الوصل الأساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="receipt_number" class="form-label">رقم الوصل</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                           value="<?php echo $edit_data['receipt_number'] ?? ''; ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="receipt_date" class="form-label">تاريخ الوصل</label>
                                    <input type="date" class="form-control" id="receipt_date" name="receipt_date" 
                                           value="<?php echo $edit_data['receipt_date'] ?? date('Y-m-d'); ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="directorate_name" class="form-label">المديرية أو المصلحة</label>
                                    <input type="text" class="form-control" id="directorate_name" name="directorate_name" 
                                           value="<?php echo $edit_data['directorate_name'] ?? ''; ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supplier_id" class="form-label">الممون</label>
                                    <select class="form-select" id="supplier_id" name="supplier_id">
                                        <option value="">اختر الممون</option>
                                        <?php foreach ($suppliers as $supplier): ?>
                                            <option value="<?php echo $supplier['id']; ?>" 
                                                    <?php echo (isset($edit_data['supplier_id']) && $edit_data['supplier_id'] == $supplier['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($supplier['full_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>


                        </div>

                        <!-- جدول المواد -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5><i class="fas fa-list me-2"></i>المواد</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="itemsTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="5%">الرقم</th>
                                                <th width="25%">اسم المادة</th>
                                                <th width="15%">رمز المادة</th>
                                                <th width="15%">رقم الجرد</th>
                                                <th width="10%">الكمية</th>
                                                <th width="12%">السعر الوحدوي</th>
                                                <th width="12%">المبلغ الإجمالي</th>
                                                <th width="6%">إجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemsTableBody">
                                            <?php if (!empty($edit_items)): ?>
                                                <?php foreach ($edit_items as $index => $item): ?>
                                                    <tr>
                                                        <td class="item-number"><?php echo $index + 1; ?></td>
                                                        <td><input type="text" class="form-control" name="items[<?php echo $index; ?>][item_name]" value="<?php echo htmlspecialchars($item['item_name']); ?>" required></td>
                                                        <td><input type="text" class="form-control" name="items[<?php echo $index; ?>][item_code]" value="<?php echo htmlspecialchars($item['item_code']); ?>"></td>
                                                        <td><input type="text" class="form-control" name="items[<?php echo $index; ?>][inventory_number]" value="<?php echo htmlspecialchars($item['inventory_number']); ?>"></td>
                                                        <td><input type="number" class="form-control quantity" name="items[<?php echo $index; ?>][quantity]" value="<?php echo $item['quantity']; ?>" step="0.01" min="0.01" required></td>
                                                        <td><input type="number" class="form-control unit-price" name="items[<?php echo $index; ?>][unit_price]" value="<?php echo $item['unit_price']; ?>" step="0.01" min="0" required></td>
                                                        <td><input type="number" class="form-control total-amount" name="items[<?php echo $index; ?>][total_amount]" value="<?php echo $item['total_amount']; ?>" readonly></td>
                                                        <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td class="item-number">1</td>
                                                    <td><input type="text" class="form-control" name="items[0][item_name]" required></td>
                                                    <td><input type="text" class="form-control" name="items[0][item_code]"></td>
                                                    <td><input type="text" class="form-control" name="items[0][inventory_number]"></td>
                                                    <td><input type="number" class="form-control quantity" name="items[0][quantity]" value="1" step="0.01" min="0.01" required></td>
                                                    <td><input type="number" class="form-control unit-price" name="items[0][unit_price]" step="0.01" min="0" required></td>
                                                    <td><input type="number" class="form-control total-amount" name="items[0][total_amount]" readonly></td>
                                                    <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="mb-3">
                                    <button type="button" class="btn btn-success" id="addItem">
                                        <i class="fas fa-plus me-2"></i>إضافة مادة
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- المجاميع - تم توسيع العمود وحذف العملة -->
                        <div class="row">
                            <div class="col-md-8 ms-auto">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>المجموع الفرعي:</strong></td>
                                        <td class="text-end fs-5"><span id="subtotal">0.00</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الضريبة:</strong></td>
                                        <td class="text-end fs-5"><span id="tax-amount">0.00</span></td>
                                    </tr>
                                    <tr class="table-success">
                                        <td><strong>المجموع الإجمالي:</strong></td>
                                        <td class="text-end fs-3 text-primary"><strong><span id="grand-total">0.00</span> دج</strong></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- التوقيعات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5><i class="fas fa-signature me-2"></i>التوقيعات</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                                   name="signature_warehouse_manager" 
                                                   <?php echo (isset($edit_data['signature_warehouse_manager']) && $edit_data['signature_warehouse_manager']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="signature_warehouse_manager">
                                                المكلف بتسيير المخزن
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                                   name="signature_means_chief"
                                                   <?php echo (isset($edit_data['signature_means_chief']) && $edit_data['signature_means_chief']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="signature_means_chief">
                                                رئيس مصلحة الوسائل
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                                   name="signature_sub_director"
                                                   <?php echo (isset($edit_data['signature_sub_director']) && $edit_data['signature_sub_director']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="signature_sub_director">
                                                المدير الفرعي للإدارة العامة
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg me-2">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo ($action === 'edit') ? 'تحديث' : 'حفظ'; ?>
                                </button>
                                
                                <button type="button" class="btn btn-info btn-lg me-2" onclick="previewPrint()">
                                    <i class="fas fa-eye me-2"></i>معاينة قبل الطباعة
                                </button>
                                
                                <button type="button" class="btn btn-secondary btn-lg me-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                                
                                <?php if ($action === 'edit'): ?>
                                    <a href="goods_entry_multi.php" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الوصولات -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة وصولات دخول السلع
                    </h5>
                </div>

                <div class="card-body">
                    <!-- البحث -->
                    <form method="GET" class="d-flex mb-3">
                        <input type="text" name="search" class="form-control me-2"
                               placeholder="بحث برقم الوصل أو المديرية أو الممون..."
                               value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if ($search): ?>
                            <a href="goods_entry_multi.php" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </form>

                    <?php if (empty($receipts)): ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo $search ? 'لا توجد نتائج للبحث المحدد' : 'لا توجد وصولات مسجلة بعد'; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الوصل</th>
                                        <th>التاريخ</th>
                                        <th>المديرية</th>
                                        <th>الممون</th>
                                        <th>عدد المواد</th>
                                        <th>المجموع الإجمالي</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th class="no-print-column">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($receipts as $receipt): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($receipt['receipt_number']); ?></strong>
                                            </td>
                                            <td>
                                                <?php echo date('d/m/Y', strtotime($receipt['receipt_date'])); ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($receipt['directorate_name']); ?>
                                            </td>
                                            <td>
                                                <?php echo $receipt['supplier_name'] ? htmlspecialchars($receipt['supplier_name']) : '<span class="text-muted">غير محدد</span>'; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $receipt['total_items']; ?></span>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($receipt['grand_total'], 2); ?></strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('d/m/Y H:i', strtotime($receipt['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td class="no-print-column">
                                                <div class="btn-group" role="group">
                                                    <a href="goods_entry_multi.php?action=edit&id=<?php echo $receipt['id']; ?>"
                                                       class="btn btn-outline-primary btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="viewReceipt(<?php echo $receipt['id']; ?>)"
                                                            class="btn btn-outline-info btn-sm" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button onclick="deleteReceipt(<?php echo $receipt['id']; ?>)"
                                                            class="btn btn-outline-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let itemIndex = <?php echo !empty($edit_items) ? count($edit_items) : 1; ?>;

// إضافة مادة جديدة
document.getElementById('addItem').addEventListener('click', function() {
    const tbody = document.getElementById('itemsTableBody');
    const newRow = document.createElement('tr');

    newRow.innerHTML = `
        <td class="item-number">${itemIndex + 1}</td>
        <td><input type="text" class="form-control" name="items[${itemIndex}][item_name]" required></td>
        <td><input type="text" class="form-control" name="items[${itemIndex}][item_code]"></td>
        <td><input type="text" class="form-control" name="items[${itemIndex}][inventory_number]"></td>
        <td><input type="number" class="form-control quantity" name="items[${itemIndex}][quantity]" value="1" step="0.01" min="0.01" required></td>
        <td><input type="number" class="form-control unit-price" name="items[${itemIndex}][unit_price]" step="0.01" min="0" required></td>
        <td><input type="number" class="form-control total-amount" name="items[${itemIndex}][total_amount]" readonly></td>
        <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
    `;

    tbody.appendChild(newRow);
    itemIndex++;

    // إضافة أحداث للحقول الجديدة
    addRowEvents(newRow);
    updateRowNumbers();
});

// حذف مادة
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-item')) {
        const row = e.target.closest('tr');
        const tbody = document.getElementById('itemsTableBody');

        if (tbody.children.length > 1) {
            row.remove();
            updateRowNumbers();
            calculateTotals();
        } else {
            showWarning('يجب أن يحتوي الوصل على مادة واحدة على الأقل');
        }
    }
});

// تحديث أرقام الصفوف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#itemsTableBody tr');
    rows.forEach((row, index) => {
        const numberCell = row.querySelector('.item-number');
        if (numberCell) {
            numberCell.textContent = index + 1;
        }

        // تحديث أسماء الحقول
        const inputs = row.querySelectorAll('input');
        inputs.forEach(input => {
            if (input.name) {
                input.name = input.name.replace(/items\[\d+\]/, `items[${index}]`);
            }
        });
    });
}

// إضافة أحداث للصف
function addRowEvents(row) {
    const quantityInput = row.querySelector('.quantity');
    const unitPriceInput = row.querySelector('.unit-price');
    const totalAmountInput = row.querySelector('.total-amount');

    function calculateRowTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const total = quantity * unitPrice;

        totalAmountInput.value = total.toFixed(2);
        calculateTotals();
    }

    quantityInput.addEventListener('input', calculateRowTotal);
    unitPriceInput.addEventListener('input', calculateRowTotal);
}

// حساب المجاميع
function calculateTotals() {
    let subtotal = 0;

    document.querySelectorAll('.total-amount').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    const taxAmount = 0; // يمكن إضافة حساب الضريبة لاحقاً
    const grandTotal = subtotal + taxAmount;

    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('tax-amount').textContent = taxAmount.toFixed(2);
    document.getElementById('grand-total').textContent = grandTotal.toFixed(2);
}



// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
        document.getElementById('goodsEntryForm').reset();

        // إعادة تعيين الجدول
        const tbody = document.getElementById('itemsTableBody');
        tbody.innerHTML = `
            <tr>
                <td class="item-number">1</td>
                <td><input type="text" class="form-control" name="items[0][item_name]" required></td>
                <td><input type="text" class="form-control" name="items[0][item_code]"></td>
                <td><input type="text" class="form-control" name="items[0][inventory_number]"></td>
                <td><input type="number" class="form-control quantity" name="items[0][quantity]" value="1" step="0.01" min="0.01" required></td>
                <td><input type="number" class="form-control unit-price" name="items[0][unit_price]" step="0.01" min="0" required></td>
                <td><input type="number" class="form-control total-amount" name="items[0][total_amount]" readonly></td>
                <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
            </tr>
        `;

        itemIndex = 1;
        addRowEvents(tbody.querySelector('tr'));
        calculateTotals();
    }
}

// معاينة الطباعة
function previewPrint() {
    // التحقق من وجود بيانات
    const receiptNumber = document.getElementById('receipt_number').value;
    const directorate = document.getElementById('directorate_name').value;

    if (!receiptNumber || !directorate) {
        showError('يرجى ملء البيانات الأساسية أولاً');
        return;
    }

    // جمع بيانات المواد
    const items = [];
    document.querySelectorAll('#itemsTableBody tr').forEach((row, index) => {
        const itemName = row.querySelector(`input[name="items[${index}][item_name]"]`).value;
        const itemCode = row.querySelector(`input[name="items[${index}][item_code]"]`).value;
        const inventoryNumber = row.querySelector(`input[name="items[${index}][inventory_number]"]`).value;
        const quantity = row.querySelector(`input[name="items[${index}][quantity]"]`).value;
        const unitPrice = row.querySelector(`input[name="items[${index}][unit_price]"]`).value;
        const totalAmount = row.querySelector(`input[name="items[${index}][total_amount]"]`).value;

        if (itemName && unitPrice) {
            items.push({
                number: index + 1,
                name: itemName,
                code: itemCode,
                inventory: inventoryNumber,
                quantity: quantity,
                unitPrice: unitPrice,
                total: totalAmount
            });
        }
    });

    if (items.length === 0) {
        showError('يرجى إضافة مادة واحدة على الأقل');
        return;
    }

    // إنشاء نافذة المعاينة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    const receiptDate = document.getElementById('receipt_date').value;
    const supplierSelect = document.getElementById('supplier_id');
    const supplierName = supplierSelect.options[supplierSelect.selectedIndex].text;


    const subtotal = document.getElementById('subtotal').textContent;
    const grandTotal = document.getElementById('grand-total').textContent;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>وصل دخول السلع إلى المخزن</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .header h1 { font-size: 18px; font-weight: bold; margin: 5px 0; }
                .header h2 { font-size: 16px; font-weight: bold; margin: 5px 0; }
                .header h3 { font-size: 14px; font-weight: bold; margin: 5px 0; }
                .info-table { width: 100%; margin: 20px 0; border-collapse: collapse; }
                .info-table td { padding: 8px; border: 1px solid #333; }
                .materials-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .materials-table th, .materials-table td { border: 1px solid #333; padding: 8px; text-align: center; }
                .materials-table th { background-color: #f0f0f0; font-weight: bold; }
                .signature-row { display: grid; grid-template-columns: 33.33% 33.33% 33.33%; margin: 30px 0; gap: 0; }
                .signature-box { border: none; padding: 20px 5px; text-align: center; min-height: 80px; font-size: 12px; font-weight: bold; }
                @media print { body { margin: 5mm; font-size: 12px; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                <h2>وزارة التربية الوطنية</h2>
                <h3>الديوان الوطني للامتحانات والمسابقات</h3>
            </div>

            <table class="info-table">
                <tr>
                    <td style="width: 50%; text-align: right; font-weight: bold;">
                        رقم الوصل: ${receiptNumber}<br>
                        المديرية أو المصلحة: ${directorate}
                    </td>
                    <td style="width: 50%; text-align: left; font-weight: bold;">
                        التاريخ: ${receiptDate}<br>
                        الممون: ${supplierName !== 'اختر الممون' ? supplierName : ''}
                    </td>
                </tr>
            </table>

            <table class="materials-table">
                <thead>
                    <tr>
                        <th width="5%">الرقم</th>
                        <th width="30%">اسم المادة</th>
                        <th width="15%">رمز المادة</th>
                        <th width="15%">رقم الجرد</th>
                        <th width="10%">الكمية</th>
                        <th width="12%">السعر الوحدوي</th>
                        <th width="13%">المبلغ الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => `
                        <tr>
                            <td>${item.number}</td>
                            <td style="text-align: right; font-weight: bold;">${item.name}</td>
                            <td>${item.code}</td>
                            <td>${item.inventory}</td>
                            <td>${item.quantity}</td>
                            <td>${item.unitPrice}</td>
                            <td><strong>${item.total}</strong></td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr style="background-color: #f0f0f0;">
                        <td colspan="6" style="text-align: right; font-weight: bold;">المجموع الإجمالي:</td>
                        <td style="font-weight: bold; font-size: 14px;">${grandTotal}</td>
                    </tr>
                </tfoot>
            </table>

            <div class="signature-row">
                <div class="signature-box">إمضاء وختم<br>رئيس مصلحة<br>الوسائل</div>
                <div class="signature-box">إمضاء وختم<br>المكلف بتسيير<br>المخزن</div>
                <div class="signature-box">إمضاء وختم<br>المدير الفرعي<br>للإدارة العامة</div>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أحداث للصفوف الموجودة
    document.querySelectorAll('#itemsTableBody tr').forEach(addRowEvents);

    // حساب المجاميع الأولية
    calculateTotals();
});

// عرض وصل
function viewReceipt(id) {
    window.open(`goods_entry_view.php?id=${id}`, '_blank', 'width=800,height=600');
}

// حذف وصل
function deleteReceipt(id) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا الوصل؟ لن يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `goods_entry_multi.php?action=delete&id=${id}`;
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
