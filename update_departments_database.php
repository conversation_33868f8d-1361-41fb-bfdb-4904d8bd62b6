<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$success_messages = [];
$error_messages = [];

try {
    // التحقق من وجود الأعمدة الجديدة
    $stmt = $db->query("SHOW COLUMNS FROM departments_services_offices");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $new_columns = [
        'internal_number' => "VARCHAR(20) AFTER name",
        'rank' => "VARCHAR(100) AFTER internal_number", 
        'manager_name' => "VARCHAR(255) AFTER rank",
        'manager_rank' => "VARCHAR(100) AFTER manager_name",
        'internal_phone' => "VARCHAR(20) AFTER manager_rank",
        'email' => "VARCHAR(100) AFTER internal_phone",
        'description' => "TEXT AFTER email",
        'display_order' => "INT DEFAULT 0 AFTER description"
    ];
    
    // إضافة الأعمدة الجديدة
    foreach ($new_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            $sql = "ALTER TABLE departments_services_offices ADD COLUMN $column $definition";
            $db->exec($sql);
            $success_messages[] = "تم إضافة عمود $column بنجاح";
        } else {
            $success_messages[] = "عمود $column موجود بالفعل";
        }
    }
    
    // إضافة الفهارس
    $indexes = [
        'idx_internal_number' => 'internal_number',
        'idx_rank' => 'rank', 
        'idx_display_order' => 'display_order'
    ];
    
    foreach ($indexes as $index_name => $column) {
        try {
            $db->exec("ALTER TABLE departments_services_offices ADD INDEX $index_name ($column)");
            $success_messages[] = "تم إضافة فهرس $index_name بنجاح";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                $success_messages[] = "فهرس $index_name موجود بالفعل";
            } else {
                $error_messages[] = "خطأ في إضافة فهرس $index_name: " . $e->getMessage();
            }
        }
    }
    
    // تحديث البيانات الموجودة
    $db->exec("UPDATE departments_services_offices SET 
        internal_number = CONCAT('INT-', LPAD(id, 3, '0')),
        display_order = id * 10
        WHERE internal_number IS NULL OR internal_number = ''");
    
    $db->exec("UPDATE departments_services_offices SET 
        rank = CASE 
            WHEN type = 'مديرية' THEN 'مدير'
            WHEN type = 'مصلحة' THEN 'رئيس مصلحة'
            WHEN type = 'مكتب' THEN 'رئيس مكتب'
            WHEN type = 'خلية' THEN 'رئيس خلية'
            ELSE 'غير محدد'
        END
        WHERE rank IS NULL OR rank = ''");
    
    $success_messages[] = "تم تحديث البيانات الموجودة بنجاح";
    
} catch (PDOException $e) {
    $error_messages[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة بيانات المديريات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-database me-2"></i>تحديث قاعدة بيانات المديريات والمصالح</h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($success_messages)): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>تم التحديث بنجاح:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($success_messages as $message): ?>
                                        <li><?php echo $message; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error_messages)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>أخطاء:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($error_messages as $message): ?>
                                        <li><?php echo $message; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="text-center mt-4">
                            <a href="departments.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>الانتقال إلى صفحة المديريات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
