-- إضافة بيانات تجريبية للولايات الجزائرية
-- Adding sample data for Algerian Wilayas

-- حذف البيانات القديمة (اختياري)
-- DELETE FROM national_office_branches;

-- إضافة فروع الولايات الجزائرية مع الإحداثيات الجغرافية

-- الولايات الشمالية
INSERT INTO national_office_branches (branch_name, address, latitude, longitude, manager_name, email, working_hours, services, phone, is_active) VALUES
('فرع الجزائر', 'شارع ديدوش مراد، الجزائر العاصمة', 36.7538, 3.0588, 'أحمد بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '021-XX-XX-XX', 1),
('فرع وهران', 'شارع الأمير عبد القادر، وهران', 35.6969, -0.6331, 'محمد بن يوسف', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '041-XX-XX-XX', 1),
('فرع قسنطينة', 'شارع العربي بن مهيدي، قسنطينة', 36.3650, 6.6147, 'عبد الرحمن بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '031-XX-XX-XX', 1),
('فرع عنابة', 'شارع الثورة، عنابة', 36.9000, 7.7667, 'عائشة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '038-XX-XX-XX', 1),
('فرع بجاية', 'شارع الحرية، بجاية', 36.7525, 5.0689, 'فاطمة بنت أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '034-XX-XX-XX', 1),
('فرع تيزي وزو', 'شارع عبان رمضان، تيزي وزو', 36.7118, 4.0435, 'يوسف بن إبراهيم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '026-XX-XX-XX', 1),
('فرع البليدة', 'شارع بن بولعيد، البليدة', 36.4203, 2.8329, 'خالد بن عمر', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '025-XX-XX-XX', 1),
('فرع بومرداس', 'شارع الاستقلال، بومرداس', 36.7667, 3.4833, 'نور الدين بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '024-XX-XX-XX', 1),
('فرع تيبازة', 'شارع الجمهورية، تيبازة', 36.5889, 2.4467, 'سعاد بنت عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '024-XX-XX-XX', 1),
('فرع الشلف', 'شارع الشهداء، الشلف', 36.1667, 1.3333, 'محمد الأمين بن سالم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '027-XX-XX-XX', 1);

-- الولايات الداخلية
INSERT INTO national_office_branches (branch_name, address, latitude, longitude, manager_name, email, working_hours, services, phone, is_active) VALUES
('فرع سطيف', 'شارع 8 مايو 1945، سطيف', 36.1833, 5.4167, 'عبد الله بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '036-XX-XX-XX', 1),
('فرع باتنة', 'شارع الاستقلال، باتنة', 35.5559, 6.1742, 'عبد الرحمن بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '033-XX-XX-XX', 1),
('فرع أم البواقي', 'شارع العربي بن مهيدي، أم البواقي', 35.8742, 7.0917, 'يوسف بن إبراهيم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '032-XX-XX-XX', 1),
('فرع خنشلة', 'شارع الثورة، خنشلة', 35.4056, 7.1378, 'أمينة بنت عبد الرحمن', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '032-XX-XX-XX', 1),
('فرع تبسة', 'شارع الحرية، تبسة', 35.4056, 8.1167, 'صالح بن أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '037-XX-XX-XX', 1),
('فرع سوق أهراس', 'شارع الجمهورية، سوق أهراس', 36.2833, 7.9500, 'فريدة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '037-XX-XX-XX', 1),
('فرع قالمة', 'شارع الاستقلال، قالمة', 36.4500, 7.4167, 'عبد الكريم بن يوسف', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '037-XX-XX-XX', 1),
('فرع جيجل', 'شارع الثورة، جيجل', 36.8000, 5.7667, 'زينب بنت علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '034-XX-XX-XX', 1),
('فرع سكيكدة', 'شارع 20 أوت، سكيكدة', 36.8667, 6.9000, 'حسن بن عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '038-XX-XX-XX', 1),
('فرع ميلة', 'شارع الحرية، ميلة', 36.4500, 6.2667, 'ليلى بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '031-XX-XX-XX', 1);

-- الولايات الغربية
INSERT INTO national_office_branches (branch_name, address, latitude, longitude, manager_name, email, working_hours, services, phone, is_active) VALUES
('فرع تلمسان', 'شارع الأمير عبد القادر، تلمسان', 34.8833, -1.3167, 'عبد الرحمن بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '043-XX-XX-XX', 1),
('فرع سيدي بلعباس', 'شارع الاستقلال، سيدي بلعباس', 35.2000, -0.6333, 'محمد بن يوسف', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '048-XX-XX-XX', 1),
('فرع مستغانم', 'شارع الثورة، مستغانم', 35.9167, 0.1167, 'فاطمة بنت أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '045-XX-XX-XX', 1),
('فرع معسكر', 'شارع الجمهورية، معسكر', 35.3833, 0.0833, 'عبد الله بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '045-XX-XX-XX', 1),
('فرع سعيدة', 'شارع الحرية، سعيدة', 34.8667, 0.1500, 'نور الدين بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '048-XX-XX-XX', 1),
('فرع تيارت', 'شارع الاستقلال، تيارت', 35.3667, 1.3167, 'سعاد بنت عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '046-XX-XX-XX', 1),
('فرع عين تموشنت', 'شارع الثورة، عين تموشنت', 35.3000, -1.1333, 'أحمد بن سالم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '043-XX-XX-XX', 1),
('فرع غليزان', 'شارع الجمهورية، غليزان', 36.1667, 1.3667, 'خديجة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '046-XX-XX-XX', 1);

-- الولايات الجنوبية
INSERT INTO national_office_branches (branch_name, address, latitude, longitude, manager_name, email, working_hours, services, phone, is_active) VALUES
('فرع ورقلة', 'شارع الاستقلال، ورقلة', 31.9500, 5.3333, 'عبد الرحمن بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع غرداية', 'شارع الثورة، غرداية', 32.4911, 3.6736, 'سعاد بنت عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع بسكرة', 'شارع الحرية، بسكرة', 34.8500, 5.7333, 'محمد بن يوسف', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '033-XX-XX-XX', 1),
('فرع الوادي', 'شارع الجمهورية، الوادي', 33.5000, 6.8667, 'فاطمة بنت أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '032-XX-XX-XX', 1),
('فرع تقرت', 'شارع الاستقلال، تقرت', 33.1167, 6.0667, 'عبد الله بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع الأغواط', 'شارع الثورة، الأغواط', 33.8000, 2.9000, 'نور الدين بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع الجلفة', 'شارع الحرية، الجلفة', 34.6667, 3.2500, 'سعاد بنت عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '027-XX-XX-XX', 1),
('فرع المسيلة', 'شارع الجمهورية، المسيلة', 35.7000, 4.5333, 'أحمد بن سالم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '035-XX-XX-XX', 1),
('فرع المدية', 'شارع الاستقلال، المدية', 36.2667, 2.7500, 'خديجة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '025-XX-XX-XX', 1),
('فرع البويرة', 'شارع الثورة، البويرة', 36.3667, 3.9000, 'عبد الرحمن بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '026-XX-XX-XX', 1);

-- الولايات الصحراوية والمنتدبة
INSERT INTO national_office_branches (branch_name, address, latitude, longitude, manager_name, email, working_hours, services, phone, is_active) VALUES
('فرع تمنراست', 'شارع الوحدة الأفريقية، تمنراست', 22.7833, 5.5167, 'محمد بن يوسف', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع أدرار', 'شارع الاستقلال، أدرار', 27.8667, -0.2833, 'فاطمة بنت أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع إليزي', 'شارع الثورة، إليزي', 26.5000, 8.4667, 'عبد الله بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع تندوف', 'شارع الجمهورية، تندوف', 27.6667, -8.1333, 'نور الدين بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع بشار', 'شارع الحرية، بشار', 31.6167, -2.2167, 'سعاد بنت عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع النعامة', 'شارع الاستقلال، النعامة', 33.2667, -0.3000, 'أحمد بن سالم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع البيض', 'شارع الثورة، البيض', 33.6833, 1.0000, 'خديجة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1);

-- الولايات المنتدبة الجديدة (2019-2021)
INSERT INTO national_office_branches (branch_name, address, latitude, longitude, manager_name, email, working_hours, services, phone, is_active) VALUES
('فرع تيميمون', 'شارع الجمهورية، تيميمون', 29.2500, 0.2333, 'عبد الرحمن بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع برج باجي مختار', 'شارع الاستقلال، برج باجي مختار', 21.3167, 0.2500, 'محمد بن يوسف', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع أولاد جلال', 'شارع الثورة، أولاد جلال', 34.3833, 5.3833, 'فاطمة بنت أحمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '033-XX-XX-XX', 1),
('فرع بني عباس', 'شارع الحرية، بني عباس', 30.1333, -2.1667, 'عبد الله بن محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '049-XX-XX-XX', 1),
('فرع عين صالح', 'شارع الجمهورية، عين صالح', 27.2167, 2.5000, 'نور الدين بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع عين قزام', 'شارع الاستقلال، عين قزام', 26.1333, 5.8833, 'سعاد بنت عبد الله', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع جانت', 'شارع الثورة، جانت', 24.5500, 9.4833, 'أحمد بن سالم', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع المغير', 'شارع الحرية، المغير', 33.9000, 5.9167, 'خديجة بنت محمد', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1),
('فرع المنيعة', 'شارع الجمهورية، المنيعة', 30.5833, 2.8667, 'عبد الرحمن بن علي', '<EMAIL>', 'الأحد - الخميس: 8:00 - 16:30', 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات', '029-XX-XX-XX', 1);

-- إنشاء فهارس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_branches_wilaya ON national_office_branches(branch_name);
CREATE INDEX IF NOT EXISTS idx_branches_active ON national_office_branches(is_active);
CREATE INDEX IF NOT EXISTS idx_branches_manager ON national_office_branches(manager_name);

-- عرض إحصائيات النتيجة
SELECT 
    COUNT(*) as total_branches,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_branches,
    COUNT(CASE WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN 1 END) as branches_with_coordinates,
    COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as branches_with_email
FROM national_office_branches;

-- عرض قائمة الولايات المضافة
SELECT branch_name, manager_name, is_active 
FROM national_office_branches 
ORDER BY branch_name;
