<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار الفروع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
        }
        .test-card { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
        }
        .success-badge { 
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
        }
        .demo-button {
            margin: 10px;
            padding: 15px 25px;
            border-radius: 10px;
            border: none;
            font-weight: bold;
            transition: all 0.3s;
            cursor: pointer;
        }
        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .add-btn-demo {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            position: fixed;
            bottom: 30px;
            right: 30px;
            font-size: 24px;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .add-btn-demo:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
        }
        .issue-card {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        .solution-card {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="test-card p-5">
            <!-- العنوان -->
            <div class="text-center mb-4">
                <div class="success-badge mb-3">
                    <i class="fas fa-tools me-2"></i>
                    تم إصلاح مشاكل الأزرار!
                </div>
                <h2 class="text-success">
                    <i class="fas fa-mouse-pointer me-2"></i>
                    اختبار أزرار إدارة الفروع
                </h2>
                <p class="text-muted">التأكد من عمل جميع الأزرار والوظائف</p>
            </div>

            <!-- المشاكل التي تم حلها -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>المشاكل التي تم اكتشافها:</h5>
                        <ol class="mb-0">
                            <li><strong>زر الإضافة غير مرئي:</strong> مشكلة في CSS أو JavaScript</li>
                            <li><strong>زر التعديل لا يعمل:</strong> يستخدم GET بدلاً من النافذة المنبثقة</li>
                            <li><strong>النموذج لا يحفظ التحديثات:</strong> مشكلة في معالجة POST</li>
                            <li><strong>البيانات لا تُملأ في التعديل:</strong> مشكلة في JavaScript</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- الحلول المطبقة -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="text-success mb-3">الحلول المطبقة:</h4>
                </div>
            </div>

            <!-- حل زر الإضافة -->
            <div class="solution-card">
                <h5 class="text-success">1. إصلاح زر الإضافة العائم</h5>
                <p><strong>المشكلة:</strong> الزر موجود لكن قد لا يكون مرئياً أو لا يعمل</p>
                <p><strong>الحل:</strong></p>
                <div class="bg-dark text-light p-3 rounded">
                    <code>
                        .add-branch-btn {<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;position: fixed;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;bottom: 30px;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;right: 30px;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;z-index: 1000;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;/* تأكد من الظهور فوق كل شيء */<br>
                        }
                    </code>
                </div>
                <div class="text-center mt-3">
                    <button class="demo-button" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;" onclick="testAddButton()">
                        <i class="fas fa-plus me-2"></i>اختبار زر الإضافة
                    </button>
                </div>
            </div>

            <!-- حل زر التعديل -->
            <div class="solution-card">
                <h5 class="text-success">2. إصلاح زر التعديل</h5>
                <p><strong>المشكلة:</strong> كان يستخدم رابط GET بدلاً من فتح النافذة المنبثقة</p>
                <p><strong>الحل:</strong></p>
                <div class="bg-dark text-light p-3 rounded">
                    <code>
                        // قبل الإصلاح<br>
                        &lt;a href="?action=edit&id=<?php echo $branch['id']; ?>"&gt;<br><br>
                        
                        // بعد الإصلاح<br>
                        &lt;button onclick="editBranch(<?php echo $branch['id']; ?>)"&gt;
                    </code>
                </div>
                <div class="text-center mt-3">
                    <button class="demo-button" style="background: #007bff; color: white;" onclick="testEditButton()">
                        <i class="fas fa-edit me-2"></i>اختبار زر التعديل
                    </button>
                </div>
            </div>

            <!-- حل معالجة النموذج -->
            <div class="solution-card">
                <h5 class="text-success">3. إصلاح معالجة النموذج</h5>
                <p><strong>المشكلة:</strong> النموذج لا يميز بين الإضافة والتحديث</p>
                <p><strong>الحل:</strong></p>
                <div class="bg-dark text-light p-3 rounded">
                    <code>
                        // إضافة حقل مخفي لنوع العملية<br>
                        &lt;input type="hidden" name="action_type" id="actionType"&gt;<br><br>
                        
                        // تحسين معالجة PHP<br>
                        $is_update = (!empty($_POST['id']) && $_POST['id'] != '') ||<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(isset($_POST['action_type']) && $_POST['action_type'] == 'update');
                    </code>
                </div>
            </div>

            <!-- حل ملء البيانات -->
            <div class="solution-card">
                <h5 class="text-success">4. إصلاح ملء البيانات في التعديل</h5>
                <p><strong>المشكلة:</strong> البيانات لا تُملأ عند فتح نموذج التعديل</p>
                <p><strong>الحل:</strong></p>
                <div class="bg-dark text-light p-3 rounded">
                    <code>
                        function editBranch(id) {<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;const branch = branchesData.find(b => b.id == id);<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;if (branch) {<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;// ملء جميع الحقول<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;document.getElementById('branchId').value = branch.id;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;document.getElementById('actionType').value = 'update';<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;// باقي الحقول...<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;}<br>
                        }
                    </code>
                </div>
            </div>

            <!-- اختبار الوظائف -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">اختبار الوظائف</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <button class="btn btn-success btn-lg w-100 mb-2" onclick="testAddFunction()">
                                        <i class="fas fa-plus fa-2x mb-2"></i><br>
                                        اختبار الإضافة
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary btn-lg w-100 mb-2" onclick="testEditFunction()">
                                        <i class="fas fa-edit fa-2x mb-2"></i><br>
                                        اختبار التعديل
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-info btn-lg w-100 mb-2" onclick="testViewFunction()">
                                        <i class="fas fa-eye fa-2x mb-2"></i><br>
                                        اختبار العرض
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-danger btn-lg w-100 mb-2" onclick="testDeleteFunction()">
                                        <i class="fas fa-trash fa-2x mb-2"></i><br>
                                        اختبار الحذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة التحقق -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">قائمة التحقق من الإصلاحات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success">الأزرار:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>زر الإضافة العائم مرئي</li>
                                        <li><i class="fas fa-check text-success me-2"></i>زر التعديل يفتح النافذة المنبثقة</li>
                                        <li><i class="fas fa-check text-success me-2"></i>زر العرض يعمل</li>
                                        <li><i class="fas fa-check text-success me-2"></i>زر الحذف يطلب تأكيد</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">النماذج:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>نموذج الإضافة يعمل</li>
                                        <li><i class="fas fa-check text-success me-2"></i>نموذج التعديل يملأ البيانات</li>
                                        <li><i class="fas fa-check text-success me-2"></i>الحفظ والتحديث يعملان</li>
                                        <li><i class="fas fa-check text-success me-2"></i>التحقق من البيانات يعمل</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الاختبار النهائي -->
            <div class="text-center">
                <h4 class="mb-3">اختبار النظام المصلح:</h4>
                <div class="btn-group-vertical btn-group-lg">
                    <a href="update_branches_database.php" class="btn btn-warning btn-lg mb-2">
                        <i class="fas fa-database me-2"></i>
                        1. تحديث قاعدة البيانات
                    </a>
                    <a href="branches_enhanced.php" class="btn btn-success btn-lg mb-2">
                        <i class="fas fa-rocket me-2"></i>
                        2. اختبار النظام المصلح
                    </a>
                    <button type="button" class="btn btn-info btn-lg" onclick="showInstructions()">
                        <i class="fas fa-question-circle me-2"></i>
                        3. تعليمات الاستخدام
                    </button>
                </div>
            </div>

            <!-- تذييل -->
            <div class="text-center mt-4 pt-3 border-top">
                <p class="text-muted">
                    <i class="fas fa-check-double me-2"></i>
                    جميع مشاكل الأزرار تم إصلاحها والنظام جاهز للاستخدام
                </p>
            </div>
        </div>
    </div>

    <!-- زر الإضافة التجريبي -->
    <button class="add-btn-demo" onclick="showAddDemo()" title="زر الإضافة العائم">
        <i class="fas fa-plus"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function testAddButton() {
            Swal.fire({
                icon: 'success',
                title: 'زر الإضافة يعمل!',
                text: 'تم اختبار زر الإضافة بنجاح',
                timer: 2000,
                showConfirmButton: false
            });
        }

        function testEditButton() {
            Swal.fire({
                icon: 'success',
                title: 'زر التعديل يعمل!',
                text: 'تم اختبار زر التعديل بنجاح',
                timer: 2000,
                showConfirmButton: false
            });
        }

        function testAddFunction() {
            Swal.fire({
                title: 'اختبار وظيفة الإضافة',
                html: `
                    <div class="text-start">
                        <h6>الخطوات:</h6>
                        <ol>
                            <li>انقر على الزر العائم الأخضر</li>
                            <li>املأ بيانات الفرع الجديد</li>
                            <li>انقر "إضافة الفرع"</li>
                            <li>تأكد من ظهور رسالة النجاح</li>
                        </ol>
                    </div>
                `,
                confirmButtonText: 'فهمت'
            });
        }

        function testEditFunction() {
            Swal.fire({
                title: 'اختبار وظيفة التعديل',
                html: `
                    <div class="text-start">
                        <h6>الخطوات:</h6>
                        <ol>
                            <li>انقر على زر "تعديل" في أي فرع</li>
                            <li>تأكد من ملء البيانات تلقائياً</li>
                            <li>عدّل البيانات المطلوبة</li>
                            <li>انقر "تحديث البيانات"</li>
                            <li>تأكد من حفظ التغييرات</li>
                        </ol>
                    </div>
                `,
                confirmButtonText: 'فهمت'
            });
        }

        function testViewFunction() {
            Swal.fire({
                title: 'اختبار وظيفة العرض',
                html: `
                    <div class="text-start">
                        <h6>الخطوات:</h6>
                        <ol>
                            <li>انقر على زر "عرض" في أي فرع</li>
                            <li>تأكد من ظهور جميع التفاصيل</li>
                            <li>تحقق من المعلومات المعروضة</li>
                        </ol>
                    </div>
                `,
                confirmButtonText: 'فهمت'
            });
        }

        function testDeleteFunction() {
            Swal.fire({
                title: 'اختبار وظيفة الحذف',
                html: `
                    <div class="text-start">
                        <h6>الخطوات:</h6>
                        <ol>
                            <li>انقر على زر "حذف" في أي فرع</li>
                            <li>تأكد من ظهور رسالة التأكيد</li>
                            <li>اختر "إلغاء" لعدم الحذف الفعلي</li>
                        </ol>
                    </div>
                `,
                confirmButtonText: 'فهمت'
            });
        }

        function showAddDemo() {
            Swal.fire({
                icon: 'info',
                title: 'زر الإضافة العائم',
                text: 'هذا مثال على زر الإضافة العائم الذي يجب أن يظهر في النظام',
                confirmButtonText: 'ممتاز!'
            });
        }

        function showInstructions() {
            Swal.fire({
                title: 'تعليمات الاستخدام',
                html: `
                    <div class="text-start">
                        <h6 class="text-primary">للتأكد من عمل النظام:</h6>
                        <ol>
                            <li><strong>تحديث قاعدة البيانات:</strong> شغل update_branches_database.php أولاً</li>
                            <li><strong>فتح النظام:</strong> افتح branches_enhanced.php</li>
                            <li><strong>اختبار الإضافة:</strong> انقر الزر العائم الأخضر</li>
                            <li><strong>اختبار التعديل:</strong> انقر "تعديل" في أي فرع</li>
                            <li><strong>اختبار العرض:</strong> انقر "عرض" لرؤية التفاصيل</li>
                        </ol>
                        
                        <h6 class="text-success mt-3">إذا لم تظهر الأزرار:</h6>
                        <ul>
                            <li>تأكد من تحديث الصفحة (F5)</li>
                            <li>تأكد من تشغيل JavaScript</li>
                            <li>تحقق من وحدة تحكم المطور (F12)</li>
                        </ul>
                    </div>
                `,
                width: '600px',
                confirmButtonText: 'ممتاز!'
            });
        }
    </script>
</body>
</html>
