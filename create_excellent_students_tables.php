<?php
require_once 'includes/config.php';

echo "<h2>إنشاء جداول التلاميذ المتفوقين</h2>";

try {
    // جدول التلاميذ الأوائل المتفوقون في البكالوريا
    $sql1 = "CREATE TABLE IF NOT EXISTS excellent_students_bac (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_number INT NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        national_id VARCHAR(18) DEFAULT NULL COMMENT 'رقم بطاقة التعريف الوطنية',
        birth_date DATE NOT NULL,
        birth_place VARCHAR(100) NOT NULL,
        average DECIMAL(4,2) NOT NULL,
        branch VARCHAR(100) NOT NULL,
        wilaya VARCHAR(100) NOT NULL,
        assignment TEXT,
        student_signature TEXT,
        fingerprint TEXT,
        exam_year YEAR NOT NULL,
        category ENUM('general', 'military_school', 'special_needs') DEFAULT 'general',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_year_category (exam_year, category),
        INDEX idx_average (average DESC)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول التلاميذ الأوائل المتفوقون في التعليم المتوسط
    $sql2 = "CREATE TABLE IF NOT EXISTS excellent_students_bem (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_number INT NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        national_id VARCHAR(18) DEFAULT NULL COMMENT 'رقم بطاقة التعريف الوطنية',
        birth_date DATE NOT NULL,
        birth_place VARCHAR(100) NOT NULL,
        average DECIMAL(4,2) NOT NULL,
        branch VARCHAR(100) NOT NULL,
        wilaya VARCHAR(100) NOT NULL,
        assignment TEXT,
        student_signature TEXT,
        fingerprint TEXT,
        exam_year YEAR NOT NULL,
        category ENUM('general', 'military_school', 'special_needs') DEFAULT 'general',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_year_category (exam_year, category),
        INDEX idx_average (average DESC)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // تنفيذ الاستعلامات
    $db->exec($sql1);
    echo "<p style='color: green;'>✓ تم إنشاء جدول التلاميذ المتفوقين في البكالوريا</p>";

    $db->exec($sql2);
    echo "<p style='color: green;'>✓ تم إنشاء جدول التلاميذ المتفوقين في التعليم المتوسط</p>";

    // إضافة عمود رقم بطاقة التعريف للجداول الموجودة (إذا لم يكن موجوداً)
    try {
        $db->exec("ALTER TABLE excellent_students_bac ADD COLUMN national_id VARCHAR(18) DEFAULT NULL COMMENT 'رقم بطاقة التعريف الوطنية' AFTER first_name");
        echo "<p style='color: blue;'>✓ تم إضافة عمود رقم بطاقة التعريف لجدول البكالوريا</p>";
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠ عمود رقم بطاقة التعريف موجود مسبقاً في جدول البكالوريا</p>";
    }

    try {
        $db->exec("ALTER TABLE excellent_students_bem ADD COLUMN national_id VARCHAR(18) DEFAULT NULL COMMENT 'رقم بطاقة التعريف الوطنية' AFTER first_name");
        echo "<p style='color: blue;'>✓ تم إضافة عمود رقم بطاقة التعريف لجدول التعليم المتوسط</p>";
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠ عمود رقم بطاقة التعريف موجود مسبقاً في جدول التعليم المتوسط</p>";
    }

    // إضافة بيانات تجريبية للبكالوريا
    $sample_bac_data = [
        // البكالوريا العامة
        [1, 'بن علي', 'أحمد', '2005-03-15', 'الجزائر', 18.50, 'علوم تجريبية', 'الجزائر', 'جامعة الجزائر', '', '', 2024, 'general'],
        [2, 'بن محمد', 'فاطمة', '2005-07-22', 'وهران', 18.25, 'رياضيات', 'وهران', 'جامعة وهران', '', '', 2024, 'general'],
        [3, 'بن يوسف', 'خالد', '2005-01-10', 'قسنطينة', 18.00, 'تقني رياضي', 'قسنطينة', 'جامعة قسنطينة', '', '', 2024, 'general'],
        
        // أشبال الأمة - البكالوريا
        [1, 'العسكري', 'محمد', '2005-05-12', 'بليدة', 17.80, 'علوم تجريبية', 'البليدة', 'الأكاديمية العسكرية', '', '', 2024, 'military_school'],
        [2, 'الحربي', 'عمر', '2005-09-08', 'سطيف', 17.60, 'رياضيات', 'سطيف', 'الأكاديمية العسكرية', '', '', 2024, 'military_school'],
        
        // ذوي الاحتياجات الخاصة - البكالوريا
        [1, 'بن سالم', 'نور الدين', '2005-11-20', 'تيزي وزو', 17.40, 'آداب وفلسفة', 'تيزي وزو', 'جامعة تيزي وزو', '', '', 2024, 'special_needs']
    ];

    $stmt_bac = $db->prepare("INSERT INTO excellent_students_bac 
        (student_number, last_name, first_name, birth_date, birth_place, average, branch, wilaya, assignment, student_signature, fingerprint, exam_year, category) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    foreach ($sample_bac_data as $data) {
        $stmt_bac->execute($data);
    }

    echo "<p style='color: green;'>✓ تم إضافة بيانات تجريبية للبكالوريا</p>";

    // إضافة بيانات تجريبية للتعليم المتوسط
    $sample_bem_data = [
        // التعليم المتوسط العام
        [1, 'بن عيسى', 'سارة', '2009-04-18', 'الجزائر', 19.50, 'عام', 'الجزائر', 'ثانوية الجزائر', '', '', 2024, 'general'],
        [2, 'بن حسن', 'كريم', '2009-08-25', 'وهران', 19.25, 'عام', 'وهران', 'ثانوية وهران', '', '', 2024, 'general'],
        [3, 'بن عمر', 'أمينة', '2009-02-14', 'قسنطينة', 19.00, 'عام', 'قسنطينة', 'ثانوية قسنطينة', '', '', 2024, 'general'],
        
        // أشبال الأمة - التعليم المتوسط
        [1, 'الجندي', 'يوسف', '2009-06-30', 'بليدة', 18.80, 'عام', 'البليدة', 'ثانوية عسكرية', '', '', 2024, 'military_school'],
        [2, 'المحارب', 'زكريا', '2009-10-12', 'سطيف', 18.60, 'عام', 'سطيف', 'ثانوية عسكرية', '', '', 2024, 'military_school'],
        
        // ذوي الاحتياجات الخاصة - التعليم المتوسط
        [1, 'بن رشيد', 'هدى', '2009-12-05', 'تيزي وزو', 18.40, 'عام', 'تيزي وزو', 'ثانوية تيزي وزو', '', '', 2024, 'special_needs']
    ];

    $stmt_bem = $db->prepare("INSERT INTO excellent_students_bem 
        (student_number, last_name, first_name, birth_date, birth_place, average, branch, wilaya, assignment, student_signature, fingerprint, exam_year, category) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    foreach ($sample_bem_data as $data) {
        $stmt_bem->execute($data);
    }

    echo "<p style='color: green;'>✓ تم إضافة بيانات تجريبية للتعليم المتوسط</p>";

    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0;'>✅ تم إنشاء جداول التلاميذ المتفوقين بنجاح!</h3>";
    echo "<p style='color: #155724; margin: 10px 0 0 0;'>تم إنشاء الجداول وإضافة البيانات التجريبية</p>";
    echo "</div>";

    echo "<p><a href='excellent_students.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى جدول التلاميذ المتفوقين</a></p>";

} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ خطأ في إنشاء الجداول</h3>";
    echo "<p style='color: #721c24; margin: 10px 0 0 0;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
a { margin: 5px; }
</style>
