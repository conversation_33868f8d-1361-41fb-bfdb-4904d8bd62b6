<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'إدارة الممونين';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = sanitizeInput($_POST['full_name']);
    $commercial_register = sanitizeInput($_POST['commercial_register']);
    $bank_postal_account = sanitizeInput($_POST['bank_postal_account']);
    $tax_id = sanitizeInput($_POST['tax_id']);
    $statistical_id = sanitizeInput($_POST['statistical_id']);
    $address = sanitizeInput($_POST['address']);
    $phone = sanitizeInput($_POST['phone']);
    $fax = sanitizeInput($_POST['fax']);
    $email = sanitizeInput($_POST['email']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            // التحقق من صحة البريد الإلكتروني
            if (!empty($email) && !isValidEmail($email)) {
                throw new Exception('عنوان البريد الإلكتروني غير صحيح');
            }
            
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE suppliers SET 
                    full_name = ?, commercial_register = ?, bank_postal_account = ?, 
                    tax_id = ?, statistical_id = ?, address = ?, 
                    phone = ?, fax = ?, email = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $full_name, $commercial_register, $bank_postal_account,
                    $tax_id, $statistical_id, $address,
                    $phone, $fax, $email, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث بيانات الممون بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO suppliers 
                    (full_name, commercial_register, bank_postal_account, tax_id, 
                     statistical_id, address, phone, fax, email) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $full_name, $commercial_register, $bank_postal_account,
                    $tax_id, $statistical_id, $address,
                    $phone, $fax, $email
                ]);
                
                $success_message = 'تم إضافة الممون بنجاح';
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM suppliers WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف الممون بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM suppliers WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE full_name LIKE ? OR email LIKE ? OR phone LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT * FROM suppliers $where_clause ORDER BY full_name ASC");
$stmt->execute($params);
$suppliers = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    إدارة الممونين
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate>
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">اسم ولقب الممون *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo $edit_data['full_name'] ?? ''; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم ولقب الممون</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="commercial_register" class="form-label">رقم السجل التجاري</label>
                            <input type="text" class="form-control" id="commercial_register" name="commercial_register" 
                                   value="<?php echo $edit_data['commercial_register'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="bank_postal_account" class="form-label">كشف الحساب البنكي أو البريدي</label>
                            <input type="text" class="form-control" id="bank_postal_account" name="bank_postal_account" 
                                   value="<?php echo $edit_data['bank_postal_account'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="tax_id" class="form-label">رقم التعريف الجبائي</label>
                            <input type="text" class="form-control" id="tax_id" name="tax_id" 
                                   value="<?php echo $edit_data['tax_id'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="statistical_id" class="form-label">رقم التعريف الإحصائي</label>
                            <input type="text" class="form-control" id="statistical_id" name="statistical_id" 
                                   value="<?php echo $edit_data['statistical_id'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="<?php echo $edit_data['phone'] ?? ''; ?>"
                                   placeholder="مثال: 0555123456 أو +213555123456"
                                   title="أدخل رقم الهاتف">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="fax" class="form-label">رقم الفاكس</label>
                            <input type="tel" class="form-control" id="fax" name="fax" 
                                   value="<?php echo $edit_data['fax'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">عنوان البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo $edit_data['email'] ?? ''; ?>">
                            <div class="invalid-feedback">يرجى إدخال عنوان بريد إلكتروني صحيح</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo $edit_data['address'] ?? ''; ?></textarea>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="suppliers.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                            </div>
                            
                            <!-- أزرار الإرسال -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary dropdown-toggle" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-share me-1"></i> إرسال
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('gmail')">
                                        <i class="fab fa-google me-2"></i> Gmail
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('outlook')">
                                        <i class="fab fa-microsoft me-2"></i> Outlook
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('yahoo')">
                                        <i class="fab fa-yahoo me-2"></i> Yahoo
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendWhatsApp()">
                                        <i class="fab fa-whatsapp me-2"></i> WhatsApp
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الممونين
                </h5>
                
                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" 
                           placeholder="بحث بالاسم أو البريد أو الهاتف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="suppliers.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>رقم الهاتف</th>
                                <th class="text-truncate-print">البريد الإلكتروني</th>
                                <th>السجل التجاري</th>
                                <th>تاريخ الإضافة</th>
                                <th class="no-print-column">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($suppliers)): ?>
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($suppliers as $supplier): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($supplier['full_name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($supplier['phone']): ?>
                                                <a href="tel:<?php echo $supplier['phone']; ?>" class="text-decoration-none">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <?php echo htmlspecialchars($supplier['phone']); ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-truncate-print">
                                            <?php if ($supplier['email']): ?>
                                                <a href="mailto:<?php echo $supplier['email']; ?>" class="text-decoration-none">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    <?php echo htmlspecialchars($supplier['email']); ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($supplier['commercial_register']) ?: '-'; ?></td>
                                        <td><?php echo formatArabicDate($supplier['created_at']); ?></td>
                                        <td class="no-print-column">
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $supplier['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteSupplier(<?php echo $supplier['id']; ?>)" 
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="contactSupplier('<?php echo $supplier['email']; ?>', '<?php echo $supplier['phone']; ?>')" 
                                                        class="btn btn-outline-success" title="اتصال">
                                                    <i class="fas fa-phone"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف الممون
function deleteSupplier(id) {
    confirmDelete('هل أنت متأكد من حذف هذا الممون؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// الاتصال بالممون
function contactSupplier(email, phone) {
    let options = [];
    
    if (email) {
        options.push({
            text: `إرسال بريد إلكتروني إلى ${email}`,
            value: 'email'
        });
    }
    
    if (phone) {
        options.push({
            text: `الاتصال على ${phone}`,
            value: 'phone'
        });
        
        options.push({
            text: `إرسال رسالة WhatsApp إلى ${phone}`,
            value: 'whatsapp'
        });
    }
    
    if (options.length === 0) {
        showWarning('لا توجد معلومات اتصال متاحة لهذا الممون');
        return;
    }
    
    // عرض خيارات الاتصال
    const optionsHtml = options.map(option => 
        `<button class="btn btn-outline-primary btn-block mb-2" onclick="executeContact('${option.value}', '${email}', '${phone}')">${option.text}</button>`
    ).join('');
    
    Swal.fire({
        title: 'اختر طريقة الاتصال',
        html: optionsHtml,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'إلغاء'
    });
}

// تنفيذ الاتصال
function executeContact(type, email, phone) {
    Swal.close();
    
    switch(type) {
        case 'email':
            window.open(`mailto:${email}`, '_blank');
            break;
        case 'phone':
            window.open(`tel:${phone}`, '_blank');
            break;
        case 'whatsapp':
            const cleanPhone = phone.replace(/\D/g, '');
            const whatsappPhone = cleanPhone.startsWith('213') ? cleanPhone : '213' + cleanPhone.substring(1);
            window.open(`https://wa.me/${whatsappPhone}`, '_blank');
            break;
    }
}

// التحقق من صحة البريد الإلكتروني
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    if (email && !isValidEmail(email)) {
        this.setCustomValidity('عنوان البريد الإلكتروني غير صحيح');
    } else {
        this.setCustomValidity('');
    }
});

// التحقق من صحة رقم الهاتف (تحذير فقط - لا يمنع الإرسال)
document.getElementById('phone').addEventListener('blur', function() {
    const phone = this.value.trim();
    const warningDiv = document.getElementById('phone-warning') || createWarningDiv();

    if (phone && phone.length > 0 && !isValidPhone(phone)) {
        warningDiv.innerHTML = '<small class="text-warning"><i class="fas fa-exclamation-triangle"></i> تحذير: رقم الهاتف قد لا يكون صحيحاً</small>';
        warningDiv.style.display = 'block';
    } else {
        warningDiv.style.display = 'none';
    }

    // لا نستخدم setCustomValidity لتجنب منع الإرسال
});

function createWarningDiv() {
    const warningDiv = document.createElement('div');
    warningDiv.id = 'phone-warning';
    warningDiv.style.display = 'none';
    document.getElementById('phone').parentNode.appendChild(warningDiv);
    return warningDiv;
}

// دالة التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة التحقق من صحة رقم الهاتف (مرنة جداً)
function isValidPhone(phone) {
    // إذا كان الحقل فارغ، فهو صحيح
    if (!phone || phone.trim() === '') {
        return true;
    }

    // إزالة جميع الرموز والمسافات
    const cleanPhone = phone.replace(/[\s\-\(\)\+\.]/g, '');

    // التحقق الأساسي: يجب أن يحتوي على أرقام فقط وطوله مناسب
    const basicRegex = /^[0-9]{6,15}$/;

    // إذا كان يحتوي على أرقام فقط وطوله مناسب، فهو صحيح
    return basicRegex.test(cleanPhone);
}

// دالة الطباعة
function printPage() {
    // إخفاء عناصر لا نريد طباعتها
    const elementsToHide = document.querySelectorAll('.no-print, .btn, .alert, .card-header, .form-control, .form-label, form');
    elementsToHide.forEach(element => {
        element.style.display = 'none';
    });

    // إضافة عنوان للطباعة
    const printTitle = document.createElement('div');
    printTitle.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 10px;">
            <h1 style="margin: 5px 0; font-size: 18px; font-weight: bold;">الجمهورية الجزائرية الديمقراطية الشعبية</h1>
            <h2 style="margin: 5px 0; font-size: 16px; font-weight: bold;">وزارة التربية الوطنية</h2>
            <h3 style="margin: 5px 0; font-size: 14px; font-weight: bold;">الديوان الوطني للامتحانات والمسابقات</h3>
            <h4 style="margin: 15px 0 5px 0; font-size: 16px; font-weight: bold;">قائمة الممونين</h4>
            <p style="margin: 5px 0; font-size: 12px;">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-DZ')}</p>
        </div>
    `;

    // إدراج العنوان في بداية الصفحة
    document.body.insertBefore(printTitle, document.body.firstChild);

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر المخفية بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(element => {
            element.style.display = '';
        });
        document.body.removeChild(printTitle);
    }, 1000);
}
</script>

<?php include 'includes/footer.php'; ?>
