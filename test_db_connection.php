<?php
echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    require_once 'includes/config.php';
    
    echo "<p style='color: green;'>✓ تم تحميل ملف الإعدادات بنجاح</p>";
    
    if (isset($db) && $db) {
        echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
        
        // فحص الجدول
        $stmt = $db->query("SHOW TABLES LIKE 'departments_services_offices'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ جدول departments_services_offices موجود</p>";
            
            // فحص الأعمدة
            $stmt = $db->query("DESCRIBE departments_services_offices");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>الأعمدة الموجودة:</h3>";
            echo "<ul>";
            foreach ($columns as $column) {
                echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
            }
            echo "</ul>";
            
            // عدد السجلات
            $stmt = $db->query("SELECT COUNT(*) as total FROM departments_services_offices");
            $count = $stmt->fetch()['total'];
            echo "<p><strong>عدد السجلات:</strong> $count</p>";
            
            // اختبار البيانات
            if ($count > 0) {
                $stmt = $db->query("SELECT * FROM departments_services_offices LIMIT 3");
                $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<h3>عينة من البيانات:</h3>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr>";
                foreach (array_keys($sample_data[0]) as $header) {
                    echo "<th style='padding: 5px; background: #f0f0f0;'>$header</th>";
                }
                echo "</tr>";
                
                foreach ($sample_data as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td style='padding: 5px;'>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ جدول departments_services_offices غير موجود</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ فشل الاتصال بقاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
table { margin: 10px 0; }
ul { padding-right: 20px; }
li { margin: 5px 0; }
</style>
