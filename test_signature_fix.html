<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التوقيعات - ضمن حدود الجدول</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            background: #2c3e50;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .problem-demo {
            background: #ffebee;
            border: 2px solid #f44336;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .solution-demo {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .materials-table th,
        .materials-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
        }
        
        .materials-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .signature-grid {
            display: grid;
            grid-template-columns: 8% 45% 12% 18% 17%;
            gap: 0;
            margin: 20px 0;
            border: 2px dashed #4caf50;
            background: #f8fff8;
        }
        
        .signature-grid-problem {
            display: grid;
            grid-template-columns: 8% 45% 12% 18% 17%;
            gap: 0;
            margin: 20px 0;
            border: 2px dashed #f44336;
            background: #fff8f8;
        }
        
        .signature-box {
            border: none;
            border-bottom: 1px solid #333;
            padding: 15px 3px;
            text-align: center;
            min-height: 60px;
            font-size: 11px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.2;
            margin: 8px 1px;
        }
        
        .signature-box-problem {
            border: none;
            border-bottom: 1px solid #333;
            padding: 25px 5px;
            text-align: center;
            min-height: 70px;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 15px 2px;
            background: #ffebee;
        }
        
        .signature-box.empty, .signature-box-problem.empty {
            border: none;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        
        .error {
            background: #ffcdd2;
            color: #c62828;
        }
        
        .success {
            background: #c8e6c9;
            color: #2e7d32;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        
        .before {
            background: #ffebee;
            border: 1px solid #f44336;
        }
        
        .after {
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
    </style>
</head>
<body>

<div class="test-container">
    <div class="test-title">🔧 إصلاح التوقيعات - ضمن حدود الجدول</div>
    
    <h3>📋 المشكلة التي تم حلها:</h3>
    <div class="problem-demo">
        <div class="status error">❌ المشكلة المكتشفة</div>
        <p><strong>التوقيعات تظهر خارج حدود الجدول:</strong></p>
        <ul>
            <li>❌ النص طويل جداً: "إمضاء وختم رئيس مصلحة الوسائل"</li>
            <li>❌ النص طويل جداً: "إمضاء وختم المدير الفرعي للإدارة العامة"</li>
            <li>❌ خاصية <code>white-space: nowrap</code> تمنع تقسيم النص</li>
            <li>❌ حجم الخط كبير نسبياً</li>
            <li>❌ التوقيعات تتجاوز عرض الأعمدة المحددة</li>
        </ul>
    </div>

    <h3>🔍 مقارنة قبل وبعد الإصلاح:</h3>
    
    <div class="comparison">
        <div class="before">
            <h4>❌ قبل الإصلاح (خارج الحدود)</h4>
            <div class="status error">النص يتجاوز حدود الأعمدة</div>
            
            <!-- مثال على المشكلة -->
            <div class="signature-grid-problem">
                <div class="signature-box-problem">إمضاء وختم رئيس مصلحة الوسائل</div>
                <div class="signature-box-problem empty"></div>
                <div class="signature-box-problem empty"></div>
                <div class="signature-box-problem empty"></div>
                <div class="signature-box-problem">إمضاء وختم المدير الفرعي للإدارة العامة</div>
            </div>
            
            <p style="color: #c62828; font-size: 12px;">
                ⚠️ المشكلة: النص يتجاوز حدود العمود المحدد!<br>
                - حجم خط كبير (14px)<br>
                - white-space: nowrap<br>
                - padding كبير (25px)<br>
                - النص لا يتقسم على أسطر متعددة
            </p>
        </div>
        
        <div class="after">
            <h4>✅ بعد الإصلاح (ضمن الحدود)</h4>
            <div class="status success">النص ضمن حدود الأعمدة</div>
            
            <!-- مثال على الحل -->
            <div class="signature-grid">
                <div class="signature-box">إمضاء وختم<br>رئيس مصلحة<br>الوسائل</div>
                <div class="signature-box empty"></div>
                <div class="signature-box empty"></div>
                <div class="signature-box empty"></div>
                <div class="signature-box">إمضاء وختم<br>المدير الفرعي<br>للإدارة العامة</div>
            </div>
            
            <div class="signature-grid">
                <div class="signature-box">إمضاء وختم<br>المكلف بتسيير<br>المخزن</div>
                <div class="signature-box empty"></div>
                <div class="signature-box empty"></div>
                <div class="signature-box empty"></div>
                <div class="signature-box">إمضاء وختم<br>المستفيد</div>
            </div>
            
            <p style="color: #2e7d32; font-size: 12px;">
                ✅ الحل: النص يتسع تماماً ضمن حدود العمود!<br>
                - حجم خط مناسب (11px)<br>
                - white-space: normal<br>
                - padding مناسب (15px)<br>
                - النص يتقسم على أسطر متعددة
            </p>
        </div>
    </div>

    <h3>🔧 التصحيحات المطبقة:</h3>
    <div class="solution-demo">
        <div class="status success">✅ تم الإصلاح بنجاح</div>
        
        <h4>📁 الملفات المصححة:</h4>
        <ul>
            <li><strong>print_office_supplies_enhanced.php</strong> - تحسين CSS للتوقيعات</li>
            <li><strong>print_office_supplies_new.php</strong> - تحسين CSS للتوقيعات</li>
        </ul>
        
        <h4>🎯 التحسينات المطبقة:</h4>
        
        <h5>📱 للعرض العادي:</h5>
        <ul>
            <li>✅ <strong>تقليل حجم الخط:</strong> من font-size كبير إلى (font_size - 4)px</li>
            <li>✅ <strong>تقليل المسافات:</strong> من padding: 25px 5px إلى 15px 3px</li>
            <li>✅ <strong>تقليل الارتفاع:</strong> من min-height: 70px إلى 60px</li>
            <li>✅ <strong>السماح بتقسيم النص:</strong> white-space: normal</li>
            <li>✅ <strong>تحسين تقسيم الكلمات:</strong> word-wrap: break-word</li>
            <li>✅ <strong>تحسين المسافة بين الأسطر:</strong> line-height: 1.2</li>
        </ul>
        
        <h5>🖨️ للطباعة:</h5>
        <ul>
            <li>✅ <strong>حجم خط أصغر:</strong> (font_size - 5)px</li>
            <li>✅ <strong>مسافات أقل:</strong> padding: 5px 2px</li>
            <li>✅ <strong>ارتفاع أقل:</strong> min-height: 25px</li>
            <li>✅ <strong>تحسين الطباعة:</strong> line-height: 1.1</li>
        </ul>
    </div>

    <h3>📊 مقارنة الخصائص:</h3>
    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
        <tr style="background: #f0f0f0;">
            <th style="border: 1px solid #ddd; padding: 8px;">الخاصية</th>
            <th style="border: 1px solid #ddd; padding: 8px;">قبل الإصلاح</th>
            <th style="border: 1px solid #ddd; padding: 8px;">بعد الإصلاح</th>
            <th style="border: 1px solid #ddd; padding: 8px;">الفائدة</th>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">حجم الخط</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">كبير</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">مناسب (-4px)</td>
            <td style="border: 1px solid #ddd; padding: 8px;">يتسع في العمود</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">المسافات</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">25px 5px</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">15px 3px</td>
            <td style="border: 1px solid #ddd; padding: 8px;">مساحة أكثر للنص</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">تقسيم النص</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">nowrap</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">normal</td>
            <td style="border: 1px solid #ddd; padding: 8px;">النص يتقسم على أسطر</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">معالجة الفيض</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">hidden</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">visible</td>
            <td style="border: 1px solid #ddd; padding: 8px;">النص لا يختفي</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">تقسيم الكلمات</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #c62828;">غير مفعل</td>
            <td style="border: 1px solid #ddd; padding: 8px; color: #2e7d32;">break-word</td>
            <td style="border: 1px solid #ddd; padding: 8px;">كلمات طويلة تتقسم</td>
        </tr>
    </table>

    <h3>🧪 كيفية الاختبار:</h3>
    <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; border: 1px solid #2196f3;">
        <ol>
            <li>افتح: <code>http://localhost/Sirius18/office_supplies.php</code></li>
            <li>أنشئ طلب جديد بأي بيانات</li>
            <li>أضف بعض المواد</li>
            <li>احفظ واضغط "معاينة قبل الطباعة"</li>
            <li>تحقق من التوقيعات:
                <ul>
                    <li>✅ <strong>"إمضاء وختم رئيس مصلحة الوسائل"</strong> ضمن العمود الأول</li>
                    <li>✅ <strong>"إمضاء وختم المدير الفرعي للإدارة العامة"</strong> ضمن العمود الأخير</li>
                    <li>✅ <strong>"إمضاء وختم المكلف بتسيير المخزن"</strong> ضمن العمود الأول</li>
                    <li>✅ <strong>"إمضاء وختم المستفيد"</strong> ضمن العمود الأخير</li>
                    <li>✅ <strong>جميع التوقيعات</strong> ضمن حدود الجدول</li>
                </ul>
            </li>
            <li>اختبر الطباعة للتأكد من الوضوح</li>
        </ol>
    </div>
</div>

</body>
</html>
