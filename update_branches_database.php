<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تحديث قاعدة بيانات الفروع</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; }</style>";
echo "</head>";
echo "<body>";
echo "<div class='container my-5'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database'></i> تحديث قاعدة بيانات الفروع</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<div class='alert alert-info'>";
    echo "<h5>🔄 جاري تحديث قاعدة البيانات...</h5>";
    echo "</div>";

    // التحقق من وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'national_office_branches'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("جدول national_office_branches غير موجود!");
    }
    
    echo "<div class='alert alert-success'>";
    echo "✅ جدول national_office_branches موجود";
    echo "</div>";

    // التحقق من الأعمدة الموجودة
    $stmt = $db->query("DESCRIBE national_office_branches");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='alert alert-info'>";
    echo "<h6>الأعمدة الموجودة حالياً:</h6>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul class='mb-0'>";
    $half = ceil(count($existing_columns) / 2);
    for ($i = 0; $i < $half; $i++) {
        echo "<li>{$existing_columns[$i]}</li>";
    }
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<ul class='mb-0'>";
    for ($i = $half; $i < count($existing_columns); $i++) {
        echo "<li>{$existing_columns[$i]}</li>";
    }
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    // الأعمدة الجديدة المطلوبة
    $new_columns = [
        'latitude' => "DECIMAL(10, 8) NULL COMMENT 'خط العرض'",
        'longitude' => "DECIMAL(11, 8) NULL COMMENT 'خط الطول'",
        'manager_name' => "VARCHAR(100) NULL COMMENT 'اسم مدير الفرع'",
        'email' => "VARCHAR(100) NULL COMMENT 'البريد الإلكتروني'",
        'working_hours' => "TEXT NULL COMMENT 'ساعات العمل'",
        'services' => "TEXT NULL COMMENT 'الخدمات المتاحة'"
    ];

    $missing_columns = [];
    foreach ($new_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            $missing_columns[$column_name] = $column_definition;
        }
    }

    if (!empty($missing_columns)) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ الأعمدة المفقودة:</h5>";
        echo "<ul>";
        foreach ($missing_columns as $column_name => $definition) {
            echo "<li><strong>$column_name</strong></li>";
        }
        echo "</ul>";
        echo "</div>";

        // إضافة الأعمدة المفقودة
        foreach ($missing_columns as $column_name => $column_definition) {
            $sql = "ALTER TABLE national_office_branches ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "<div class='alert alert-success'>";
            echo "✅ تم إضافة العمود: <strong>$column_name</strong>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-success'>";
        echo "✅ جميع الأعمدة المطلوبة موجودة بالفعل";
        echo "</div>";
    }

    // إضافة بيانات تجريبية للفروع
    echo "<div class='alert alert-info'>";
    echo "<h5>📍 إضافة بيانات تجريبية للفروع...</h5>";
    echo "</div>";

    $sample_data = [
        [
            'branch_name' => 'فرع الجزائر',
            'latitude' => 36.7538,
            'longitude' => 3.0588,
            'manager_name' => 'أحمد بن علي',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 - 16:30',
            'services' => 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
        ],
        [
            'branch_name' => 'فرع وهران',
            'latitude' => 35.6969,
            'longitude' => -0.6331,
            'manager_name' => 'محمد بن يوسف',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 - 16:30',
            'services' => 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
        ],
        [
            'branch_name' => 'فرع باتنة',
            'latitude' => 35.5559,
            'longitude' => 6.1742,
            'manager_name' => 'عبد الرحمن بن محمد',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 - 16:30',
            'services' => 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
        ],
        [
            'branch_name' => 'فرع بجاية',
            'latitude' => 36.7525,
            'longitude' => 5.0689,
            'manager_name' => 'فاطمة بنت أحمد',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 - 16:30',
            'services' => 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
        ],
        [
            'branch_name' => 'فرع عنابة',
            'latitude' => 36.9000,
            'longitude' => 7.7667,
            'manager_name' => 'عائشة بنت محمد',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 - 16:30',
            'services' => 'التسجيل في الامتحانات، استخراج الشهادات، الاستعلامات'
        ]
    ];

    foreach ($sample_data as $data) {
        // التحقق من وجود الفرع
        $stmt = $db->prepare("SELECT COUNT(*) FROM national_office_branches WHERE branch_name = ?");
        $stmt->execute([$data['branch_name']]);
        
        if ($stmt->fetchColumn() > 0) {
            // تحديث البيانات الموجودة
            $stmt = $db->prepare("UPDATE national_office_branches SET 
                latitude = ?, longitude = ?, manager_name = ?, email = ?, working_hours = ?, services = ?
                WHERE branch_name = ?");
            $stmt->execute([
                $data['latitude'], $data['longitude'], $data['manager_name'], 
                $data['email'], $data['working_hours'], $data['services'], $data['branch_name']
            ]);
            echo "<div class='alert alert-info'>";
            echo "🔄 تم تحديث بيانات: <strong>{$data['branch_name']}</strong>";
            echo "</div>";
        } else {
            // إضافة فرع جديد
            $stmt = $db->prepare("INSERT INTO national_office_branches 
                (branch_name, latitude, longitude, manager_name, email, working_hours, services, is_active, address, phone) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, ?)");
            $stmt->execute([
                $data['branch_name'], $data['latitude'], $data['longitude'], 
                $data['manager_name'], $data['email'], $data['working_hours'], 
                $data['services'], 'العنوان سيتم تحديده لاحقاً', '021-XX-XX-XX'
            ]);
            echo "<div class='alert alert-success'>";
            echo "✅ تم إضافة فرع جديد: <strong>{$data['branch_name']}</strong>";
            echo "</div>";
        }
    }

    // إنشاء الفهارس
    echo "<div class='alert alert-info'>";
    echo "<h5>🔍 إنشاء الفهارس...</h5>";
    echo "</div>";

    try {
        $db->exec("CREATE INDEX idx_branches_location ON national_office_branches(latitude, longitude)");
        echo "<div class='alert alert-success'>";
        echo "✅ تم إنشاء فهرس الموقع الجغرافي";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ فهرس الموقع الجغرافي موجود بالفعل";
        echo "</div>";
    }

    try {
        $db->exec("CREATE INDEX idx_branches_email ON national_office_branches(email)");
        echo "<div class='alert alert-success'>";
        echo "✅ تم إنشاء فهرس البريد الإلكتروني";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ فهرس البريد الإلكتروني موجود بالفعل";
        echo "</div>";
    }

    // التحقق النهائي
    $stmt = $db->query("SELECT COUNT(*) as total FROM national_office_branches");
    $total = $stmt->fetch()['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as with_coords FROM national_office_branches WHERE latitude IS NOT NULL AND longitude IS NOT NULL");
    $with_coords = $stmt->fetch()['with_coords'];

    echo "<div class='alert alert-success'>";
    echo "<h4>🎉 تم التحديث بنجاح!</h4>";
    echo "<p><strong>إجمالي الفروع:</strong> $total</p>";
    echo "<p><strong>فروع بإحداثيات:</strong> $with_coords</p>";
    echo "</div>";

    // عرض النتيجة النهائية
    $stmt = $db->query("SELECT id, branch_name, latitude, longitude, manager_name, email, is_active FROM national_office_branches ORDER BY branch_name");
    $final_branches = $stmt->fetchAll();
    
    echo "<div class='alert alert-info'>";
    echo "<h6>📋 قائمة الفروع النهائية:</h6>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead><tr><th>ID</th><th>اسم الفرع</th><th>المدير</th><th>البريد</th><th>الإحداثيات</th><th>الحالة</th></tr></thead>";
    echo "<tbody>";
    foreach ($final_branches as $branch) {
        $coords = ($branch['latitude'] && $branch['longitude']) ? 
                  number_format($branch['latitude'], 4) . ', ' . number_format($branch['longitude'], 4) : 
                  'غير محدد';
        $status = $branch['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">معطل</span>';
        echo "<tr>";
        echo "<td>{$branch['id']}</td>";
        echo "<td><strong>{$branch['branch_name']}</strong></td>";
        echo "<td>{$branch['manager_name']}</td>";
        echo "<td>{$branch['email']}</td>";
        echo "<td><small>$coords</small></td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ خطأ في التحديث:</h5>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='branches_enhanced.php' class='btn btn-primary btn-lg me-2'>";
echo "<i class='fas fa-external-link-alt me-2'></i>فتح نظام الفروع المحسن";
echo "</a>";
echo "<a href='branches.php' class='btn btn-secondary btn-lg'>";
echo "<i class='fas fa-building me-2'></i>النظام الأصلي";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
