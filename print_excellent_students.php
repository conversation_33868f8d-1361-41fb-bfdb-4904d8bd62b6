<?php
require_once 'includes/config.php';

$section = isset($_GET['section']) ? $_GET['section'] : 'bac_general';
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// تحديد الجدول والفئة
$table = '';
$category = '';
$exam_name = '';

switch ($section) {
    case 'bac_general':
        $table = 'excellent_students_bac';
        $category = 'general';
        $exam_name = 'التلاميذ الأوائل المتفوقون في البكالوريا';
        break;
    case 'bac_military':
        $table = 'excellent_students_bac';
        $category = 'military_school';
        $exam_name = 'أشبال الأمة - البكالوريا';
        break;
    case 'bac_special':
        $table = 'excellent_students_bac';
        $category = 'special_needs';
        $exam_name = 'ذوي الاحتياجات الخاصة - البكالوريا';
        break;
    case 'bem_general':
        $table = 'excellent_students_bem';
        $category = 'general';
        $exam_name = 'التلاميذ الأوائل المتفوقون في التعليم المتوسط';
        break;
    case 'bem_military':
        $table = 'excellent_students_bem';
        $category = 'military_school';
        $exam_name = 'أشبال الأمة - التعليم المتوسط';
        break;
    case 'bem_special':
        $table = 'excellent_students_bem';
        $category = 'special_needs';
        $exam_name = 'ذوي الاحتياجات الخاصة - التعليم المتوسط';
        break;
}

try {
    // جلب البيانات
    $stmt = $db->prepare("SELECT * FROM $table WHERE category = ? AND exam_year = ? ORDER BY average DESC, last_name ASC");
    $stmt->execute([$category, $year]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    echo "<script>alert('خطأ في قاعدة البيانات: " . $e->getMessage() . "'); window.close();</script>";
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $exam_name ?> - دورة <?= $year ?></title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #333;
            padding-bottom: 15px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }
        
        .header h2 {
            font-size: 16px;
            margin: 5px 0;
            color: #666;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #333;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }
        
        .data-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 11px;
        }
        
        .data-table td {
            font-size: 10px;
        }
        
        .data-table .number-col { width: 4%; }
        .data-table .name-col { width: 12%; }
        .data-table .date-col { width: 8%; }
        .data-table .place-col { width: 12%; }
        .data-table .average-col { width: 6%; font-weight: bold; }
        .data-table .branch-col { width: 10%; }
        .data-table .wilaya-col { width: 10%; }
        .data-table .assignment-col { width: 15%; }
        .data-table .signature-col { width: 6%; }
        .data-table .fingerprint-col { width: 6%; }
        
        .signatures-section {
            margin-top: 40px;
            page-break-inside: avoid;
        }
        
        .signatures-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .signatures-table td {
            border: 2px solid #333;
            padding: 40px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            width: 25%;
            vertical-align: top;
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #666;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>الديوان الوطني للامتحانات والمسابقات</h1>
        <h2><?= $exam_name ?></h2>
        <h2>دورة <?= $year ?></h2>
    </div>

    <?php if (empty($students)): ?>
        <div class="no-data">
            <p>لا توجد بيانات للعرض في هذا القسم</p>
        </div>
    <?php else: ?>
        <!-- Data Table -->
        <table class="data-table">
            <thead>
                <tr>
                    <th class="number-col">الرقم</th>
                    <th class="name-col">اللقب</th>
                    <th class="name-col">الاسم</th>
                    <th class="date-col">تاريخ الميلاد</th>
                    <th class="place-col">مكان الميلاد</th>
                    <th class="average-col">المعدل</th>
                    <th class="branch-col">الشعبة</th>
                    <th class="wilaya-col">الولاية</th>
                    <th class="assignment-col">التعيين</th>
                    <th class="signature-col">إمضاء المعني</th>
                    <th class="fingerprint-col">البصمة</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($students as $index => $student): ?>
                    <tr>
                        <td class="number-col"><?= $index + 1 ?></td>
                        <td class="name-col"><?= htmlspecialchars($student['last_name']) ?></td>
                        <td class="name-col"><?= htmlspecialchars($student['first_name']) ?></td>
                        <td class="date-col"><?= date('d/m/Y', strtotime($student['birth_date'])) ?></td>
                        <td class="place-col"><?= htmlspecialchars($student['birth_place']) ?></td>
                        <td class="average-col"><?= $student['average'] ?></td>
                        <td class="branch-col"><?= htmlspecialchars($student['branch']) ?></td>
                        <td class="wilaya-col"><?= htmlspecialchars($student['wilaya']) ?></td>
                        <td class="assignment-col"><?= htmlspecialchars($student['assignment'] ?: '-') ?></td>
                        <td class="signature-col"><?= $student['student_signature'] ? '✓' : '✗' ?></td>
                        <td class="fingerprint-col"><?= $student['fingerprint'] ? '✓' : '✗' ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>

    <!-- Signatures Section -->
    <div class="signatures-section">
        <table class="signatures-table">
            <tr>
                <td>
                    إمضاء و ختم المكلف بتسيير المخزن
                    <br><br><br><br>
                    التاريخ: ___/___/______
                </td>
                <td>
                    إمضاء و ختم رئيس مصلحة الوسائل
                    <br><br><br><br>
                    التاريخ: ___/___/______
                </td>
                <td>
                    إمضاء و ختم المدير الفرعي للإدارة العامة
                    <br><br><br><br>
                    التاريخ: ___/___/______
                </td>
                <td>
                    إمضاء وختم مدير الديوان الوطني للامتحانات و المسابقات
                    <br><br><br><br>
                    التاريخ: ___/___/______
                </td>
            </tr>
        </table>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            window.print();
        };
        
        // إغلاق النافذة بعد الطباعة
        window.onafterprint = function() {
            window.close();
        };
    </script>
</body>
</html>
