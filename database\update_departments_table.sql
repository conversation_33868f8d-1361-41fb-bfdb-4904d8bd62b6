-- تحديث جدول المديريات والمصالح لإضافة الحقول الجديدة
-- يجب تشغيل هذا الملف لتحديث الجدول الموجود

-- إ<PERSON><PERSON><PERSON><PERSON> عمود الرقم الداخلي
ALTER TABLE departments_services_offices 
ADD COLUMN internal_number VARCHAR(20) AFTER name,
ADD INDEX idx_internal_number (internal_number);

-- إضا<PERSON>ة عمود الرتبة
ALTER TABLE departments_services_offices 
ADD COLUMN rank VARCHAR(100) AFTER internal_number,
ADD INDEX idx_rank (rank);

-- إضافة عمود اسم المسؤول
ALTER TABLE departments_services_offices 
ADD COLUMN manager_name VARCHAR(255) AFTER rank;

-- إضا<PERSON>ة عمود رتبة المسؤول
ALTER TABLE departments_services_offices 
ADD COLUMN manager_rank VARCHAR(100) AFTER manager_name;

-- إضا<PERSON>ة عمود الهاتف الداخلي
ALTER TABLE departments_services_offices 
ADD COLUMN internal_phone VARCHAR(20) AFTER manager_rank;

-- إضافة عمود البريد الإلكتروني
ALTER TABLE departments_services_offices 
ADD COLUMN email VARCHAR(100) AFTER internal_phone;

-- إضافة عمود الوصف
ALTER TABLE departments_services_offices 
ADD COLUMN description TEXT AFTER email;

-- إضافة عمود ترتيب العرض في الهيكل التنظيمي
ALTER TABLE departments_services_offices 
ADD COLUMN display_order INT DEFAULT 0 AFTER description,
ADD INDEX idx_display_order (display_order);

-- تحديث البيانات الموجودة بأرقام داخلية تجريبية
UPDATE departments_services_offices SET 
    internal_number = CONCAT('INT-', LPAD(id, 3, '0')),
    display_order = id * 10
WHERE internal_number IS NULL;

-- إدراج بعض البيانات التجريبية للرتب
UPDATE departments_services_offices SET 
    rank = CASE 
        WHEN type = 'مديرية' THEN 'مدير'
        WHEN type = 'مصلحة' THEN 'رئيس مصلحة'
        WHEN type = 'مكتب' THEN 'رئيس مكتب'
        WHEN type = 'خلية' THEN 'رئيس خلية'
        ELSE 'غير محدد'
    END
WHERE rank IS NULL;

-- عرض النتائج المحدثة
SELECT 
    id,
    name,
    internal_number,
    rank,
    type,
    manager_name,
    manager_rank,
    internal_phone,
    email,
    display_order,
    is_active
FROM departments_services_offices 
ORDER BY display_order, type, name;
