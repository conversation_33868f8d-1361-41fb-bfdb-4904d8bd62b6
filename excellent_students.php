<?php
require_once 'includes/config.php';

// الحصول على السنة المحددة أو السنة الحالية
$selected_year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// الحصول على القسم المحدد
$selected_section = isset($_GET['section']) ? $_GET['section'] : 'bac_general';

// إنشاء قائمة السنوات من 1962 إلى 2100
$available_years = [];
for ($year = 2100; $year >= 1962; $year--) {
    $available_years[] = $year;
}

// جلب السنوات الموجودة في قاعدة البيانات لإبرازها
$years_stmt = $db->query("
    SELECT DISTINCT exam_year FROM excellent_students_bac
    UNION
    SELECT DISTINCT exam_year FROM excellent_students_bem
    ORDER BY exam_year DESC
");
$existing_years = $years_stmt->fetchAll(PDO::FETCH_COLUMN);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول التلاميذ المتفوقين في الامتحانات المدرسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        .header-section {
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .section-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }
        .section-tab {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            min-width: 200px;
            flex: 1;
        }
        .section-tab:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        .section-tab.active {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-color: #28a745;
        }
        .year-selector {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .year-selector select {
            min-width: 200px;
        }
        .year-info {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .data-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table th {
            background: linear-gradient(45deg, #495057, #6c757d);
            color: white;
            border: none;
            padding: 15px 10px;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
        }
        .table td {
            padding: 12px 10px;
            vertical-align: middle;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .btn-custom {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .btn-add { background: #28a745; color: white; }
        .btn-edit { background: #ffc107; color: #212529; }
        .btn-save { background: #17a2b8; color: white; }
        .btn-download { background: #6f42c1; color: white; }
        .btn-print { background: #fd7e14; color: white; }
        .signatures-section {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        .national-id-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .national-id-text {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        .copy-btn {
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 4px;
        }
        .copy-btn:hover {
            background-color: #007bff;
            color: white;
        }
        .copy-success {
            animation: copySuccess 0.5s ease-in-out;
        }
        @keyframes copySuccess {
            0% { background-color: #28a745; color: white; }
            100% { background-color: transparent; color: inherit; }
        }
        .toast-message {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        .signature-box {
            text-align: center;
            flex: 1;
            min-width: 200px;
            padding: 15px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: white;
        }
        .signature-title {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 30px;
            color: #495057;
        }
        @media (max-width: 768px) {
            .section-tabs {
                flex-direction: column;
            }
            .section-tab {
                min-width: auto;
            }
            .signatures-section {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <h1><i class="fas fa-trophy"></i> جدول التلاميذ المتفوقين في الامتحانات المدرسية</h1>
            <p class="mb-0">الديوان الوطني للامتحانات والمسابقات</p>
        </div>

        <!-- Year Selector -->
        <div class="year-selector">
            <label for="yearSelect" class="form-label"><strong>اختيار السنة الدراسية:</strong></label>
            <select id="yearSelect" class="form-select d-inline-block w-auto mx-2" onchange="changeYear()">
                <?php foreach ($available_years as $year): ?>
                    <?php $has_data = in_array($year, $existing_years); ?>
                    <option value="<?= $year ?>" <?= $year == $selected_year ? 'selected' : '' ?>
                            <?= $has_data ? 'style="font-weight: bold; color: #28a745;"' : 'style="color: #6c757d;"' ?>>
                        دورة <?= $year ?><?= $has_data ? ' ✓' : '' ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <div class="year-info">
                <i class="fas fa-info-circle"></i> السنوات المميزة بـ ✓ تحتوي على بيانات طلاب |
                <span class="text-success">عدد الطلاب: <span id="total-students">0</span></span>
            </div>
        </div>

        <!-- Section Tabs -->
        <div class="section-tabs">
            <div class="section-tab <?= $selected_section == 'bac_general' ? 'active' : '' ?>" 
                 onclick="changeSection('bac_general')">
                <strong>التلاميذ الأوائل المتفوقون في البكالوريا</strong><br>
                <small>دورة <?= $selected_year ?></small>
            </div>
            <div class="section-tab <?= $selected_section == 'bac_military' ? 'active' : '' ?>" 
                 onclick="changeSection('bac_military')">
                <strong>أشبال الأمة - البكالوريا</strong><br>
                <small>دورة <?= $selected_year ?></small>
            </div>
            <div class="section-tab <?= $selected_section == 'bac_special' ? 'active' : '' ?>" 
                 onclick="changeSection('bac_special')">
                <strong>ذوي الاحتياجات الخاصة - البكالوريا</strong><br>
                <small>دورة <?= $selected_year ?></small>
            </div>
        </div>

        <div class="section-tabs">
            <div class="section-tab <?= $selected_section == 'bem_general' ? 'active' : '' ?>" 
                 onclick="changeSection('bem_general')">
                <strong>التلاميذ الأوائل المتفوقون في التعليم المتوسط</strong><br>
                <small>دورة <?= $selected_year ?></small>
            </div>
            <div class="section-tab <?= $selected_section == 'bem_military' ? 'active' : '' ?>" 
                 onclick="changeSection('bem_military')">
                <strong>أشبال الأمة - التعليم المتوسط</strong><br>
                <small>دورة <?= $selected_year ?></small>
            </div>
            <div class="section-tab <?= $selected_section == 'bem_special' ? 'active' : '' ?>" 
                 onclick="changeSection('bem_special')">
                <strong>ذوي الاحتياجات الخاصة - التعليم المتوسط</strong><br>
                <small>دورة <?= $selected_year ?></small>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-custom btn-add" onclick="addStudent()">
                <i class="fas fa-plus"></i> إضافة
            </button>
            <button class="btn btn-custom btn-edit" onclick="editStudent()">
                <i class="fas fa-edit"></i> تعديل
            </button>
            <button class="btn btn-custom btn-save" onclick="saveData()">
                <i class="fas fa-save"></i> حفظ
            </button>
            <div class="dropdown">
                <button class="btn btn-custom btn-download dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> تنزيل
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="downloadFile('pdf')"><i class="fas fa-file-pdf"></i> PDF</a></li>
                    <li><a class="dropdown-item" href="#" onclick="downloadFile('doc')"><i class="fas fa-file-word"></i> DOC</a></li>
                    <li><a class="dropdown-item" href="#" onclick="downloadFile('docx')"><i class="fas fa-file-word"></i> DOCX</a></li>
                    <li><a class="dropdown-item" href="#" onclick="downloadFile('excel')"><i class="fas fa-file-excel"></i> Excel</a></li>
                </ul>
            </div>
            <button class="btn btn-custom btn-print" onclick="printTable()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>

        <!-- Data Table Container -->
        <div id="dataTableContainer">
            <?php include 'get_excellent_students_data.php'; ?>
        </div>

        <!-- Signatures Section -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-title">إمضاء و ختم المكلف بتسيير المخزن</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">إمضاء و ختم رئيس مصلحة الوسائل</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">إمضاء و ختم المدير الفرعي للإدارة العامة</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">إمضاء وختم مدير الديوان الوطني للامتحانات و المسابقات</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSection = '<?= $selected_section ?>';
        let currentYear = <?= $selected_year ?>;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadTableData();
        });

        function changeYear() {
            const yearSelect = document.getElementById('yearSelect');
            currentYear = yearSelect.value;
            window.location.href = `excellent_students.php?year=${currentYear}&section=${currentSection}`;
        }

        function changeSection(section) {
            currentSection = section;
            window.location.href = `excellent_students.php?year=${currentYear}&section=${section}`;
        }

        function loadTableData() {
            fetch(`get_excellent_students_data.php?section=${currentSection}&year=${currentYear}`)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('dataTableContainer').innerHTML = data;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('dataTableContainer').innerHTML = 
                        '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
                });
        }

        function addStudent() {
            window.location.href = `add_excellent_student.php?section=${currentSection}&year=${currentYear}`;
        }

        function editStudent() {
            alert('يرجى تحديد طالب للتعديل من الجدول');
        }

        function saveData() {
            alert('تم حفظ البيانات بنجاح');
        }

        function downloadFile(format) {
            window.open(`export_excellent_students.php?section=${currentSection}&year=${currentYear}&format=${format}`, '_blank');
        }

        function printTable() {
            window.open(`print_excellent_students.php?section=${currentSection}&year=${currentYear}`, '_blank');
        }

        // وظيفة نسخ رقم بطاقة التعريف
        function copyNationalId(studentId) {
            const nationalIdElement = document.getElementById('national-id-' + studentId);
            const nationalId = nationalIdElement.textContent;

            // نسخ النص إلى الحافظة
            navigator.clipboard.writeText(nationalId).then(function() {
                // إظهار تأثير النجاح
                const copyBtn = nationalIdElement.parentElement.querySelector('.copy-btn');
                copyBtn.classList.add('copy-success');
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';

                // إظهار رسالة نجاح
                showToast('تم نسخ رقم بطاقة التعريف: ' + nationalId, 'success');

                // إعادة الزر لحالته الأصلية بعد ثانيتين
                setTimeout(function() {
                    copyBtn.classList.remove('copy-success');
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            }).catch(function(err) {
                console.error('فشل في نسخ النص: ', err);
                showToast('فشل في نسخ رقم بطاقة التعريف', 'error');
            });
        }

        // وظيفة إظهار رسائل التنبيه
        function showToast(message, type) {
            // إنشاء عنصر التنبيه
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} toast-message`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            `;

            // إضافة التنبيه للصفحة
            document.body.appendChild(toast);

            // إزالة التنبيه بعد 3 ثوان
            setTimeout(function() {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(function() {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
