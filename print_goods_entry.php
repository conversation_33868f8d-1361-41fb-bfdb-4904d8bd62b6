<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? 14;

if (empty($id)) {
    die('معرف الوصل مطلوب');
}

// جلب بيانات الوصل
$stmt = $db->prepare("SELECT ger.*, s.full_name as supplier_name, c.name_ar as currency_name, c.symbol as currency_symbol
                     FROM goods_entry_receipts ger 
                     LEFT JOIN suppliers s ON ger.supplier_id = s.id 
                     LEFT JOIN currencies c ON ger.currency_type = c.code
                     WHERE ger.id = ?");
$stmt->execute([$id]);
$receipt = $stmt->fetch();

if (!$receipt) {
    die('الوصل غير موجود');
}

// دالة تنسيق التاريخ العربي
function formatArabicDate($date) {
    if (empty($date)) return '';
    
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس', '04' => 'أبريل',
        '05' => 'مايو', '06' => 'يونيو', '07' => 'يوليو', '08' => 'أغسطس',
        '09' => 'سبتمبر', '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    $date_parts = explode('-', $date);
    if (count($date_parts) >= 3) {
        $day = ltrim($date_parts[2], '0');
        $month = $months[$date_parts[1]] ?? $date_parts[1];
        $year = $date_parts[0];
        return "$day $month $year";
    }
    
    return $date;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة وصل دخول السلع إلى المخزن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 15px;
            direction: rtl;
            font-size: <?php echo $font_size . 'px'; ?>;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        .header h1 { 
            color: #000; 
            font-weight: bold;
            margin: 3px 0; 
            font-size: <?php echo ($font_size + 2) . 'px'; ?>;
            text-shadow: 0.5px 0.5px 0px #333;
        }
        .header h2 { 
            color: #000; 
            font-weight: bold;
            margin: 3px 0; 
            font-size: <?php echo ($font_size + 1) . 'px'; ?>;
            text-shadow: 0.5px 0.5px 0px #333;
        }
        .header h3 { 
            color: #000; 
            font-weight: bold;
            margin: 3px 0; 
            font-size: <?php echo $font_size . 'px'; ?>;
            text-shadow: 0.5px 0.5px 0px #333;
        }
        .form-title { 
            text-align: center; 
            margin: 20px 0; 
            font-size: <?php echo ($font_size + 4) . 'px'; ?>;
            font-weight: bold; 
            color: #2c5530;
            text-decoration: underline;
        }
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: <?php echo ($font_size - 1) . 'px'; ?>;
        }
        .materials-table th,
        .materials-table td {
            border: 1px solid #333;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }
        .materials-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: <?php echo $font_size . 'px'; ?>;
        }
        .signature-row {
            display: grid;
            grid-template-columns: 20% 20% 20% 20% 20%;
            margin-bottom: 50px;
            padding: 20px 0;
            gap: 0;
        }
        .signature-box {
            border: none;
            padding: 15px 3px;
            text-align: center;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: <?php echo ($font_size - 2) . 'px'; ?>;
            font-weight: bold;
            color: #000;
            background-color: transparent;
            margin: 8px 1px;
            white-space: normal;
            overflow: visible;
            word-wrap: break-word;
            line-height: 1.3;
        }
        .signatures-section { 
            margin-top: 30px; 
            page-break-inside: avoid;
        }
        .no-print {
            display: block;
        }
        @media print {
            body {
                margin: 2mm !important;
                padding: 0 !important;
                font-size: <?php echo ($font_size - 2) . 'px'; ?>;
                line-height: 1.1;
            }
            .no-print {
                display: none !important;
            }
            .header h1, .header h2, .header h3 {
                margin: 1px 0 !important;
                padding: 0 !important;
            }
            .materials-table {
                margin: 5px 0;
                font-size: <?php echo ($font_size - 2) . 'px'; ?>;
            }
            .materials-table th, .materials-table td {
                padding: 1px 2px;
                font-size: <?php echo ($font_size - 3) . 'px'; ?>;
                border: 1px solid #000;
            }
            .signature-box {
                padding: 6px 3px;
                min-height: 30px;
                font-size: <?php echo ($font_size - 2) . 'px'; ?>;
                font-weight: bold !important;
                color: #000 !important;
                margin: 3px 0;
                white-space: normal !important;
                overflow: visible !important;
                word-wrap: break-word !important;
                line-height: 1.2 !important;
                text-align: center !important;
                border: none !important;
            }
            .signatures-section { margin-top: 8px; }
            .signature-row {
                margin-bottom: 10px;
                padding: 3px 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
        <h2>وزارة التربية الوطنية</h2>
        <h3>الديوان الوطني للامتحانات والمسابقات</h3>
    </div>

    <div class="form-title">
        وصل دخول السلع إلى المخزن
    </div>

    <!-- معلومات فوق الجدول: رقم الوصل والمديرية (يسار) + التاريخ والممون (يمين) -->
    <table style="width: 100%; border-collapse: collapse; margin: 10px 0; table-layout: fixed;">
        <tr>
            <!-- معلومات اليسار: رقم الوصل والمديرية -->
            <td style="width: 50%; text-align: right; padding: 5px; font-size: <?php echo ($font_size - 1) . 'px'; ?>; font-weight: bold; vertical-align: top;">
                رقم الوصل: <?php echo htmlspecialchars($receipt['receipt_number']); ?><br>
                المديرية أو المصلحة: <?php echo htmlspecialchars($receipt['directorate_name']); ?>
            </td>
            <!-- معلومات اليمين: التاريخ والممون -->
            <td style="width: 50%; text-align: left; padding: 5px; font-size: <?php echo ($font_size - 1) . 'px'; ?>; font-weight: bold; vertical-align: top;">
                التاريخ: <?php echo formatArabicDate($receipt['receipt_date']); ?><br>
                الممون: <?php echo htmlspecialchars($receipt['supplier_name'] ?: 'غير محدد'); ?>
            </td>
        </tr>
    </table>

    <!-- جدول المواد -->
    <table class="materials-table">
        <thead>
            <tr>
                <th width="5%">الرقم</th>
                <th width="30%">اسم المادة</th>
                <th width="15%">رقم المادة</th>
                <th width="15%">رقم الجرد</th>
                <th width="10%">السعر الوحدوي</th>
                <th width="10%">المبلغ الإجمالي</th>
                <th width="15%">العملة</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td style="text-align: right; font-weight: bold;">
                    <?php echo htmlspecialchars($receipt['item_name']); ?>
                </td>
                <td>
                    <?php echo htmlspecialchars($receipt['item_number'] ?: '-'); ?>
                </td>
                <td>
                    <?php echo htmlspecialchars($receipt['inventory_number'] ?: '-'); ?>
                </td>
                <td>
                    <?php echo number_format($receipt['unit_price'], 2); ?>
                </td>
                <td>
                    <strong><?php echo number_format($receipt['total_amount'], 2); ?></strong>
                </td>
                <td>
                    <?php echo htmlspecialchars($receipt['currency_symbol'] ?: $receipt['currency_type']); ?>
                </td>
            </tr>
        </tbody>
    </table>

    <!-- التوقيعات - تظهر دائماً -->
    <div class="signatures-section">
        <!-- الصف الأول: رئيس مصلحة الوسائل + المدير الفرعي + المكلف بتسيير المخزن -->
        <div class="signature-row">
            <div class="signature-box">إمضاء وختم<br>رئيس مصلحة<br>الوسائل</div>
            <div class="signature-box">إمضاء وختم<br>المدير الفرعي<br>للإدارة العامة</div>
            <div class="signature-box">إمضاء وختم<br>المكلف بتسيير<br>المخزن</div>
            <div class="signature-box">إمضاء وختم<br>الممون</div>
            <div class="signature-box">إمضاء وختم<br>المستلم</div>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
        <a href="goods_entry.php" class="btn btn-info btn-lg">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <script>
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
