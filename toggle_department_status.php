<?php
require_once 'config/config.php';
require_once 'config/database.php';

header('Content-Type: application/json; charset=utf-8');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموحة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['id']) || !isset($input['status'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'بيانات غير مكتملة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$id = (int)$input['id'];
$status = (bool)$input['status'];

$database = new Database();
$db = $database->getConnection();

try {
    // التحقق من وجود الوحدة الإدارية
    $stmt = $db->prepare("SELECT name, type FROM departments_services_offices WHERE id = ?");
    $stmt->execute([$id]);
    $department = $stmt->fetch();
    
    if (!$department) {
        echo json_encode([
            'success' => false,
            'message' => 'الوحدة الإدارية غير موجودة'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // تحديث الحالة
    $stmt = $db->prepare("UPDATE departments_services_offices SET is_active = ? WHERE id = ?");
    $stmt->execute([$status, $id]);
    
    $statusText = $status ? 'تم تفعيل' : 'تم إلغاء تفعيل';
    
    echo json_encode([
        'success' => true,
        'message' => $statusText . ' ' . $department['type'] . ': ' . $department['name']
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
