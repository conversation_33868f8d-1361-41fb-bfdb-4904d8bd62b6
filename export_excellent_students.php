<?php
require_once 'includes/config.php';

$section = isset($_GET['section']) ? $_GET['section'] : 'bac_general';
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$format = isset($_GET['format']) ? $_GET['format'] : 'pdf';

// تحديد الجدول والفئة
$table = '';
$category = '';
$exam_name = '';

switch ($section) {
    case 'bac_general':
        $table = 'excellent_students_bac';
        $category = 'general';
        $exam_name = 'التلاميذ الأوائل المتفوقون في إمتحان شهادة البكالوريا';
        break;
    case 'bac_military':
        $table = 'excellent_students_bac';
        $category = 'military_school';
        $exam_name = 'التلاميذ الأوائل لمدارس أشبال الأمة المتفوقون في إمتحان شهادة البكالوريا';
        break;
    case 'bac_special':
        $table = 'excellent_students_bac';
        $category = 'special_needs';
        $exam_name = 'التلاميذ الأوائل لذوي الاحتياجات الخاصة المتفوقون في إمتحان شهادة البكالوريا';
        break;
    case 'bem_general':
        $table = 'excellent_students_bem';
        $category = 'general';
        $exam_name = 'التلاميذ الأوائل المتفوقون في إمتحان شهادة التعليم المتوسط';
        break;
    case 'bem_military':
        $table = 'excellent_students_bem';
        $category = 'military_school';
        $exam_name = 'التلاميذ الأوائل لمدارس أشبال الأمة المتفوقون في إمتحان شهادة التعليم المتوسط';
        break;
    case 'bem_special':
        $table = 'excellent_students_bem';
        $category = 'special_needs';
        $exam_name = 'التلاميذ الأوائل لذوي الاحتياجات الخاصة المتفوقون في إمتحان شهادة التعليم المتوسط';
        break;
}

try {
    // جلب البيانات
    $stmt = $db->prepare("SELECT * FROM $table WHERE category = ? AND exam_year = ? ORDER BY average DESC, last_name ASC");
    $stmt->execute([$category, $year]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($students)) {
        echo "<script>alert('لا توجد بيانات للتصدير'); window.close();</script>";
        exit;
    }

    // تحديد نوع التصدير
    switch ($format) {
        case 'excel':
            exportToExcel($students, $exam_name, $year);
            break;
        case 'pdf':
            exportToPDF($students, $exam_name, $year);
            break;
        case 'doc':
        case 'docx':
            exportToWord($students, $exam_name, $year, $format);
            break;
        default:
            echo "<script>alert('صيغة غير مدعومة'); window.close();</script>";
    }

} catch (PDOException $e) {
    echo "<script>alert('خطأ في قاعدة البيانات: " . $e->getMessage() . "'); window.close();</script>";
}

function exportToExcel($students, $exam_name, $year) {
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $exam_name . '_' . $year . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');

    echo "\xEF\xBB\xBF"; // UTF-8 BOM

    echo '<table border="1" style="border-collapse: collapse; width: 100%; font-family: Arial;">';
    echo '<tr style="background-color: #4472C4; color: white; font-weight: bold;">';
    echo '<td colspan="11" style="text-align: center; padding: 15px; font-size: 16px;">';
    echo $exam_name . ' - دورة ' . $year;
    echo '</td>';
    echo '</tr>';
    
    echo '<tr style="background-color: #D9E2F3; font-weight: bold; text-align: center;">';
    echo '<td>الرقم</td>';
    echo '<td>اللقب</td>';
    echo '<td>الاسم</td>';
    echo '<td>تاريخ الميلاد</td>';
    echo '<td>مكان الميلاد</td>';
    echo '<td>المعدل</td>';
    echo '<td>الشعبة</td>';
    echo '<td>الولاية</td>';
    echo '<td>التعيين</td>';
    echo '<td>إمضاء المعني</td>';
    echo '<td>البصمة</td>';
    echo '</tr>';

    foreach ($students as $index => $student) {
        echo '<tr>';
        echo '<td style="text-align: center;">' . ($index + 1) . '</td>';
        echo '<td>' . htmlspecialchars($student['last_name']) . '</td>';
        echo '<td>' . htmlspecialchars($student['first_name']) . '</td>';
        echo '<td style="text-align: center;">' . date('d/m/Y', strtotime($student['birth_date'])) . '</td>';
        echo '<td>' . htmlspecialchars($student['birth_place']) . '</td>';
        echo '<td style="text-align: center; font-weight: bold;">' . $student['average'] . '</td>';
        echo '<td>' . htmlspecialchars($student['branch']) . '</td>';
        echo '<td>' . htmlspecialchars($student['wilaya']) . '</td>';
        echo '<td>' . htmlspecialchars($student['assignment'] ?: '-') . '</td>';
        echo '<td style="text-align: center;">' . ($student['student_signature'] ? '✓' : '✗') . '</td>';
        echo '<td style="text-align: center;">' . ($student['fingerprint'] ? '✓' : '✗') . '</td>';
        echo '</tr>';
    }

    // إضافة التوقيعات
    echo '<tr><td colspan="11" style="height: 30px;"></td></tr>';
    echo '<tr>';
    echo '<td colspan="2" style="text-align: center; border: 1px solid black; padding: 20px;">إمضاء و ختم المكلف بتسيير المخزن</td>';
    echo '<td colspan="3" style="text-align: center; border: 1px solid black; padding: 20px;">إمضاء و ختم رئيس مصلحة الوسائل</td>';
    echo '<td colspan="3" style="text-align: center; border: 1px solid black; padding: 20px;">إمضاء و ختم المدير الفرعي للإدارة العامة</td>';
    echo '<td colspan="3" style="text-align: center; border: 1px solid black; padding: 20px;">إمضاء وختم مدير الديوان الوطني للامتحانات و المسابقات</td>';
    echo '</tr>';

    echo '</table>';
}

function exportToPDF($students, $exam_name, $year) {
    // هذه دالة مبسطة - في التطبيق الحقيقي يمكن استخدام مكتبة مثل TCPDF أو FPDF
    header('Content-Type: text/html; charset=utf-8');
    
    echo '<!DOCTYPE html>';
    echo '<html lang="ar" dir="rtl">';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<title>' . $exam_name . ' - دورة ' . $year . '</title>';
    echo '<style>';
    echo 'body { font-family: Arial, sans-serif; margin: 20px; }';
    echo 'table { width: 100%; border-collapse: collapse; margin: 20px 0; }';
    echo 'th, td { border: 1px solid #000; padding: 8px; text-align: center; }';
    echo 'th { background-color: #f0f0f0; font-weight: bold; }';
    echo '.title { text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; }';
    echo '.signatures { margin-top: 50px; }';
    echo '.signature-box { display: inline-block; width: 23%; text-align: center; border: 1px solid #000; padding: 30px 10px; margin: 1%; }';
    echo '@media print { body { margin: 0; } @page { size: A4 landscape; margin: 1cm; } }';
    echo '</style>';
    echo '</head>';
    echo '<body>';
    
    echo '<div class="title">' . $exam_name . ' - دورة ' . $year . '</div>';
    
    echo '<table>';
    echo '<thead>';
    echo '<tr>';
    echo '<th>الرقم</th><th>اللقب</th><th>الاسم</th><th>تاريخ الميلاد</th><th>مكان الميلاد</th>';
    echo '<th>المعدل</th><th>الشعبة</th><th>الولاية</th><th>التعيين</th><th>إمضاء المعني</th><th>البصمة</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($students as $index => $student) {
        echo '<tr>';
        echo '<td>' . ($index + 1) . '</td>';
        echo '<td>' . htmlspecialchars($student['last_name']) . '</td>';
        echo '<td>' . htmlspecialchars($student['first_name']) . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($student['birth_date'])) . '</td>';
        echo '<td>' . htmlspecialchars($student['birth_place']) . '</td>';
        echo '<td><strong>' . $student['average'] . '</strong></td>';
        echo '<td>' . htmlspecialchars($student['branch']) . '</td>';
        echo '<td>' . htmlspecialchars($student['wilaya']) . '</td>';
        echo '<td>' . htmlspecialchars($student['assignment'] ?: '-') . '</td>';
        echo '<td>' . ($student['student_signature'] ? '✓' : '✗') . '</td>';
        echo '<td>' . ($student['fingerprint'] ? '✓' : '✗') . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    
    echo '<div class="signatures">';
    echo '<div class="signature-box">إمضاء و ختم المكلف بتسيير المخزن</div>';
    echo '<div class="signature-box">إمضاء و ختم رئيس مصلحة الوسائل</div>';
    echo '<div class="signature-box">إمضاء و ختم المدير الفرعي للإدارة العامة</div>';
    echo '<div class="signature-box">إمضاء وختم مدير الديوان الوطني للامتحانات و المسابقات</div>';
    echo '</div>';
    
    echo '<script>window.print();</script>';
    echo '</body>';
    echo '</html>';
}

function exportToWord($students, $exam_name, $year, $format) {
    if ($format == 'docx') {
        header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        header('Content-Disposition: attachment; filename="' . $exam_name . '_' . $year . '.docx"');
    } else {
        header('Content-Type: application/msword');
        header('Content-Disposition: attachment; filename="' . $exam_name . '_' . $year . '.doc"');
    }
    
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head><meta charset="utf-8"><title>' . $exam_name . '</title></head>';
    echo '<body dir="rtl">';
    
    echo '<div style="text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0;">';
    echo $exam_name . ' - دورة ' . $year;
    echo '</div>';
    
    echo '<table border="1" style="border-collapse: collapse; width: 100%; font-family: Arial;">';
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td>الرقم</td><td>اللقب</td><td>الاسم</td><td>تاريخ الميلاد</td><td>مكان الميلاد</td>';
    echo '<td>المعدل</td><td>الشعبة</td><td>الولاية</td><td>التعيين</td><td>إمضاء المعني</td><td>البصمة</td>';
    echo '</tr>';
    
    foreach ($students as $index => $student) {
        echo '<tr>';
        echo '<td>' . ($index + 1) . '</td>';
        echo '<td>' . htmlspecialchars($student['last_name']) . '</td>';
        echo '<td>' . htmlspecialchars($student['first_name']) . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($student['birth_date'])) . '</td>';
        echo '<td>' . htmlspecialchars($student['birth_place']) . '</td>';
        echo '<td><strong>' . $student['average'] . '</strong></td>';
        echo '<td>' . htmlspecialchars($student['branch']) . '</td>';
        echo '<td>' . htmlspecialchars($student['wilaya']) . '</td>';
        echo '<td>' . htmlspecialchars($student['assignment'] ?: '-') . '</td>';
        echo '<td>' . ($student['student_signature'] ? '✓' : '✗') . '</td>';
        echo '<td>' . ($student['fingerprint'] ? '✓' : '✗') . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    
    echo '<br><br>';
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr>';
    echo '<td style="text-align: center; padding: 30px;">إمضاء و ختم المكلف بتسيير المخزن</td>';
    echo '<td style="text-align: center; padding: 30px;">إمضاء و ختم رئيس مصلحة الوسائل</td>';
    echo '<td style="text-align: center; padding: 30px;">إمضاء و ختم المدير الفرعي للإدارة العامة</td>';
    echo '<td style="text-align: center; padding: 30px;">إمضاء وختم مدير الديوان الوطني للامتحانات و المسابقات</td>';
    echo '</tr>';
    echo '</table>';
    
    echo '</body></html>';
}
?>
