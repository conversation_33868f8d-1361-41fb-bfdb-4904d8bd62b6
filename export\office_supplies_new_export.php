<?php
// ملف تصدير طلب اللوازم المكتبية - التصميم الجديد
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تضمين الإعدادات
require_once '../config/config.php';

// التحقق من الطريقة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('يجب استخدام POST method');
}

// التحقق من نوع التصدير
$format = $_GET['format'] ?? 'pdf';
if (!in_array($format, ['pdf', 'docx', 'doc'])) {
    die('نوع ملف غير مدعوم');
}

// جلب البيانات الأساسية
$data = [
    'receipt_number' => sanitizeInput($_POST['receipt_number'] ?? ''),
    'request_date' => $_POST['request_date'] ?? date('Y-m-d'),
    'beneficiary_directorate' => sanitizeInput($_POST['beneficiary_directorate'] ?? ''),
    'recipient_name' => sanitizeInput($_POST['recipient_name'] ?? ''),
    'notes' => sanitizeInput($_POST['notes'] ?? ''),
    'signature_warehouse_manager' => isset($_POST['signature_warehouse_manager']),
    'signature_means_chief' => isset($_POST['signature_means_chief']),
    'signature_sub_director' => isset($_POST['signature_sub_director']),
    'signature_beneficiary' => isset($_POST['signature_beneficiary'])
];

// معالجة المواد
$materials = [];
if (isset($_POST['materials'])) {
    foreach ($_POST['materials'] as $material) {
        if (!empty($material['item_name'])) {
            $materials[] = [
                'item_name' => sanitizeInput($material['item_name']),
                'quantity' => intval($material['quantity']),
                'unit' => sanitizeInput($material['unit']),
                'supply_number' => sanitizeInput($material['supply_number'])
            ];
        }
    }
}

// التحقق من البيانات الأساسية
if (empty($data['receipt_number'])) {
    die('رقم الوصل مطلوب');
}

if (empty($materials)) {
    die('يجب إضافة مادة واحدة على الأقل');
}

// تنسيق التاريخ
$data['formatted_date'] = date('d/m/Y', strtotime($data['request_date']));

// إنشاء المحتوى
function createNewContent($data, $materials) {
    // تحديد حجم الخط حسب عدد المواد
    $materials_count = count($materials);
    $font_size = '16px';
    $table_font_size = '14px';
    $content_padding = '30px';
    $field_margin = '20px';

    if ($materials_count > 5) {
        $font_size = '14px';
        $table_font_size = '12px';
        $content_padding = '20px';
        $field_margin = '15px';
    } elseif ($materials_count > 3) {
        $font_size = '15px';
        $table_font_size = '13px';
        $content_padding = '25px';
        $field_margin = '18px';
    }
    // بناء جدول المواد
    $table_padding = $materials_count > 5 ? '8px' : '12px';
    $table_margin = $materials_count > 5 ? '20px' : '30px';
    $table_title_size = $materials_count > 5 ? '16px' : '18px';

    $materialsTableHtml = '
    <div style="margin: ' . $table_margin . ' 0;">
        <h6 style="color: #2c5530; margin-bottom: 15px; font-size: ' . $table_title_size . '; font-weight: bold;">جدول المواد المطلوبة:</h6>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: ' . $table_margin . ';">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-weight: bold; width: 8%; font-size: ' . $table_font_size . ';">الرقم</th>
                    <th style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-weight: bold; width: 40%; font-size: ' . $table_font_size . ';">اسم المادة</th>
                    <th style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-weight: bold; width: 12%; font-size: ' . $table_font_size . ';">الكمية</th>
                    <th style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-weight: bold; width: 15%; font-size: ' . $table_font_size . ';">الوحدة</th>
                    <th style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-weight: bold; width: 25%; font-size: ' . $table_font_size . ';">رقم اللوازم</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($materials as $index => $material) {
        $materialsTableHtml .= '
                <tr>
                    <td style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-size: ' . $table_font_size . ';">' . ($index + 1) . '</td>
                    <td style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: right; font-size: ' . $table_font_size . ';">' . htmlspecialchars($material['item_name']) . '</td>
                    <td style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-size: ' . $table_font_size . ';">' . $material['quantity'] . '</td>
                    <td style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-size: ' . $table_font_size . ';">' . ($material['unit'] ?: '-') . '</td>
                    <td style="border: 2px solid #000; padding: ' . $table_padding . '; text-align: center; font-size: ' . $table_font_size . ';">' . ($material['supply_number'] ?: '-') . '</td>
                </tr>';
    }
    
    $materialsTableHtml .= '
            </tbody>
        </table>
    </div>';
    
    // بناء التوقيعات 2×2
    $signaturesHtml = '';
    $signatures = [];
    
    if ($data['signature_warehouse_manager']) {
        $signatures[] = 'إمضاء وختم المكلف بتسيير المخزن';
    }
    if ($data['signature_means_chief']) {
        $signatures[] = 'إمضاء وختم رئيس مصلحة الوسائل';
    }
    if ($data['signature_sub_director']) {
        $signatures[] = 'إمضاء وختم المدير الفرعي للإدارة العامة';
    }
    if ($data['signature_beneficiary']) {
        $signatures[] = 'إمضاء المستفيد';
    }
    
    if (!empty($signatures)) {
        $signature_margin = $materials_count > 5 ? '50px' : '80px';
        $signature_padding = $materials_count > 5 ? '30px 15px' : '40px 20px';
        $signature_height = $materials_count > 5 ? '80px' : '120px';
        $signature_font_size = $materials_count > 5 ? '14px' : '16px';

        $signaturesHtml = '<div style="margin-top: ' . $signature_margin . '; page-break-inside: avoid;">';
        $signaturesHtml .= '<table style="width: 100%; border-collapse: collapse; margin-top: 30px;">';

        // الصف الأول - أول توقيعين أفقياً
        if (count($signatures) >= 1) {
            $signaturesHtml .= '<tr>';

            // التوقيع الأول
            if (isset($signatures[0])) {
                $signaturesHtml .= '<td style="text-align: center; padding: ' . $signature_padding . '; border: 3px solid #000; width: 50%; vertical-align: top;">';
                $signaturesHtml .= '<div class="signature-space"></div>';
                $signaturesHtml .= '<div class="signature-label">' . $signatures[0] . '</div>';
                $signaturesHtml .= '</td>';
            }

            // التوقيع الثاني
            if (isset($signatures[1])) {
                $signaturesHtml .= '<td style="text-align: center; padding: ' . $signature_padding . '; border: 3px solid #000; width: 50%; vertical-align: top;">';
                $signaturesHtml .= '<div class="signature-space"></div>';
                $signaturesHtml .= '<div class="signature-label">' . $signatures[1] . '</div>';
                $signaturesHtml .= '</td>';
            } else {
                // إذا كان هناك توقيع واحد فقط، اجعله يأخذ العرض كاملاً
                if (count($signatures) == 1) {
                    $signaturesHtml = str_replace('width: 50%', 'width: 100%', $signaturesHtml);
                } else {
                    // خلية فارغة
                    $signaturesHtml .= '<td style="border: 3px solid #000; width: 50%;"></td>';
                }
            }

            $signaturesHtml .= '</tr>';
        }

        // الصف الثاني - التوقيعين الثالث والرابع أفقياً
        if (count($signatures) >= 3) {
            $signaturesHtml .= '<tr>';

            // التوقيع الثالث
            if (isset($signatures[2])) {
                $signaturesHtml .= '<td style="text-align: center; padding: ' . $signature_padding . '; border: 3px solid #000; width: 50%; vertical-align: top;">';
                $signaturesHtml .= '<div class="signature-space"></div>';
                $signaturesHtml .= '<div class="signature-label">' . $signatures[2] . '</div>';
                $signaturesHtml .= '</td>';
            }

            // التوقيع الرابع
            if (isset($signatures[3])) {
                $signaturesHtml .= '<td style="text-align: center; padding: ' . $signature_padding . '; border: 3px solid #000; width: 50%; vertical-align: top;">';
                $signaturesHtml .= '<div class="signature-space"></div>';
                $signaturesHtml .= '<div class="signature-label">' . $signatures[3] . '</div>';
                $signaturesHtml .= '</td>';
            } else {
                // خلية فارغة
                $signaturesHtml .= '<td style="border: 3px solid #000; width: 50%;"></td>';
            }

            $signaturesHtml .= '</tr>';
        }

        $signaturesHtml .= '</table>';
        $signaturesHtml .= '</div>';
    }
    
    return '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>طلب واستلام اللوازم المكتبية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px 60px;
            line-height: 1.6;
            direction: rtl;
            font-size: ' . $font_size . ';
        }
        .header { 
            text-align: center; 
            margin-bottom: 50px; 
            border-bottom: 3px solid #2c5530;
            padding-bottom: 30px;
        }
        .header h1 { 
            color: #2c5530; 
            margin: 8px 0; 
            font-size: 26px;
            font-weight: bold;
        }
        .header h2 { 
            color: #4a7c59; 
            margin: 8px 0; 
            font-size: 22px;
            font-weight: 600;
        }
        .header h3 { 
            color: #666; 
            margin: 8px 0; 
            font-size: 18px;
            font-weight: 500;
        }
        .title { 
            text-align: center; 
            font-size: 32px; 
            font-weight: bold; 
            margin: 40px 0; 
            color: #2c5530;
            border: 3px solid #2c5530;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .content {
            margin: 30px 0;
            font-size: ' . $font_size . ';
            background-color: #fafafa;
            padding: ' . $content_padding . ';
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .field {
            margin: ' . $field_margin . ' 0;
            padding: 10px 0;
            border-bottom: 1px dotted #999;
            min-height: 20px;
        }
        .field strong {
            display: inline-block;
            width: 250px;
            color: #2c5530;
            font-weight: bold;
            font-size: ' . $font_size . ';
        }
        .field-value {
            font-size: ' . $font_size . ';
            color: #333;
            font-weight: 500;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px;
        }
        th, td {
            border: 2px solid #000;
            padding: ' . ($materials_count > 5 ? '8px' : '12px') . ';
            text-align: center;
            font-size: ' . $table_font_size . ';
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .signature-space {
            height: ' . ($materials_count > 5 ? '80px' : '120px') . ';
            margin-bottom: ' . ($materials_count > 5 ? '15px' : '25px') . ';
            border: 1px dashed #ccc;
            background-color: #fafafa;
        }
        .signature-label {
            border-top: 3px solid #000;
            padding-top: ' . ($materials_count > 5 ? '10px' : '20px') . ';
            font-weight: bold;
            font-size: ' . ($materials_count > 5 ? '14px' : '16px') . ';
            color: #2c5530;
        }
        @media print {
            body { 
                margin: 20mm 25mm; 
                font-size: 14px;
            }
            .content {
                background-color: white;
                border: none;
                padding: 20px 0;
            }
            .signature-space {
                background-color: white;
                border: 1px dashed #999;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>' . INSTITUTION_NAME_AR . '</h1>
        <h2>' . MINISTRY_NAME_AR . '</h2>
        <h3>' . OFFICE_NAME_AR . '</h3>
    </div>
    
    <div class="title">طلب واستلام اللوازم المكتبية</div>
    
    <div class="content">
        <div class="field">
            <strong>رقم الوصل:</strong> 
            <span class="field-value">' . htmlspecialchars($data['receipt_number']) . '</span>
        </div>
        <div class="field">
            <strong>التاريخ:</strong> 
            <span class="field-value">' . $data['formatted_date'] . '</span>
        </div>
        <div class="field">
            <strong>المديرية الفرعية أو المصلحة المستفيدة:</strong> 
            <span class="field-value">' . htmlspecialchars($data['beneficiary_directorate']) . '</span>
        </div>
        <div class="field">
            <strong>اسم ولقب المستلم:</strong> 
            <span class="field-value">' . htmlspecialchars($data['recipient_name']) . '</span>
        </div>
        ' . ($data['notes'] ? '<div class="field"><strong>الملاحظات:</strong> <span class="field-value">' . htmlspecialchars($data['notes']) . '</span></div>' : '') . '
    </div>
    
    ' . $materialsTableHtml . '
    
    ' . $signaturesHtml . '
    
    <div style="margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 20px; font-size: 14px; color: #666;">
        تم إنشاء هذا المستند تلقائياً من نظام تسيير المخزن - ' . date('Y-m-d H:i:s') . '
    </div>
</body>
</html>';
}

// إنشاء المحتوى
$content = createNewContent($data, $materials);

// تحديد نوع الملف والتنزيل
$filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '_' . date('Y-m-d');

// تنظيف المخرجات
while (ob_get_level()) {
    ob_end_clean();
}

// تحديد headers حسب نوع الملف
switch ($format) {
    case 'pdf':
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.html"');
        break;
        
    case 'docx':
        header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        header('Content-Disposition: attachment; filename="' . $filename . '.docx"');
        break;
        
    case 'doc':
        header('Content-Type: application/msword');
        header('Content-Disposition: attachment; filename="' . $filename . '.doc"');
        break;
}

header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Expires: 0');

// إرسال المحتوى
echo $content;
exit;
?>
