<?php
require_once 'includes/config.php';

echo "<h2>إضافة بيانات تجريبية للمديريات والمصالح</h2>";

try {
    // التحقق من وجود بيانات
    $stmt = $db->query("SELECT COUNT(*) as total FROM departments_services_offices");
    $existing_count = $stmt->fetch()['total'];
    
    echo "<p>عدد السجلات الحالية: $existing_count</p>";
    
    if ($existing_count > 0) {
        echo "<p style='color: blue;'>توجد بيانات مسبقاً. لن يتم إضافة بيانات جديدة.</p>";
        echo "<a href='departments.php'>الذهاب إلى صفحة المديريات</a>";
        exit;
    }
    
    // التحقق من الأعمدة المتاحة
    $stmt_check = $db->query("SHOW COLUMNS FROM departments_services_offices");
    $existing_columns = [];
    while ($row = $stmt_check->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }
    
    $has_extended_fields = in_array('internal_number', $existing_columns);
    
    // البيانات التجريبية
    $sample_data = [
        [
            'name' => 'مديرية الشؤون الإدارية والمالية',
            'type' => 'مديرية',
            'parent_id' => null,
            'internal_number' => 'DIR-001',
            'rank' => 'مدير',
            'manager_name' => 'أحمد بن محمد',
            'manager_rank' => 'مهندس دولة',
            'internal_phone' => '101',
            'email' => '<EMAIL>',
            'description' => 'تتولى الشؤون الإدارية والمالية للديوان',
            'display_order' => 1,
            'is_active' => 1
        ],
        [
            'name' => 'مديرية الامتحانات والمسابقات',
            'type' => 'مديرية',
            'parent_id' => null,
            'internal_number' => 'DIR-002',
            'rank' => 'مدير',
            'manager_name' => 'فاطمة بنت علي',
            'manager_rank' => 'أستاذ محاضر',
            'internal_phone' => '201',
            'email' => '<EMAIL>',
            'description' => 'تنظيم وإدارة الامتحانات والمسابقات الوطنية',
            'display_order' => 2,
            'is_active' => 1
        ],
        [
            'name' => 'مصلحة الموارد البشرية',
            'type' => 'مصلحة',
            'parent_id' => 1, // تابعة للمديرية الأولى
            'internal_number' => 'SRV-001',
            'rank' => 'رئيس مصلحة',
            'manager_name' => 'خالد بن عمر',
            'manager_rank' => 'مهندس رئيسي',
            'internal_phone' => '111',
            'email' => '<EMAIL>',
            'description' => 'إدارة الموارد البشرية والتوظيف',
            'display_order' => 3,
            'is_active' => 1
        ],
        [
            'name' => 'مصلحة المحاسبة',
            'type' => 'مصلحة',
            'parent_id' => 1, // تابعة للمديرية الأولى
            'internal_number' => 'SRV-002',
            'rank' => 'رئيس مصلحة',
            'manager_name' => 'عائشة بنت حسن',
            'manager_rank' => 'تقني سامي',
            'internal_phone' => '112',
            'email' => '<EMAIL>',
            'description' => 'المحاسبة والشؤون المالية',
            'display_order' => 4,
            'is_active' => 1
        ],
        [
            'name' => 'مكتب التوظيف',
            'type' => 'مكتب',
            'parent_id' => 3, // تابع لمصلحة الموارد البشرية
            'internal_number' => 'OFF-001',
            'rank' => 'رئيس مكتب',
            'manager_name' => 'محمد بن يوسف',
            'manager_rank' => 'إداري',
            'internal_phone' => '1111',
            'email' => '<EMAIL>',
            'description' => 'التوظيف والتعيينات',
            'display_order' => 5,
            'is_active' => 1
        ]
    ];
    
    echo "<h3>إضافة البيانات التجريبية:</h3>";
    
    foreach ($sample_data as $data) {
        try {
            if ($has_extended_fields) {
                $stmt = $db->prepare("INSERT INTO departments_services_offices 
                    (name, type, parent_id, internal_number, rank, manager_name, manager_rank, 
                     internal_phone, email, description, display_order, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $data['name'], $data['type'], $data['parent_id'], $data['internal_number'],
                    $data['rank'], $data['manager_name'], $data['manager_rank'],
                    $data['internal_phone'], $data['email'], $data['description'],
                    $data['display_order'], $data['is_active']
                ]);
            } else {
                $stmt = $db->prepare("INSERT INTO departments_services_offices 
                    (name, type, parent_id, is_active) 
                    VALUES (?, ?, ?, ?)");
                
                $stmt->execute([
                    $data['name'], $data['type'], $data['parent_id'], $data['is_active']
                ]);
            }
            
            echo "<p style='color: green;'>✓ تم إضافة: " . $data['type'] . " - " . $data['name'] . "</p>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في إضافة " . $data['name'] . ": " . $e->getMessage() . "</p>";
        }
    }
    
    // التحقق النهائي
    $stmt = $db->query("SELECT COUNT(*) as total FROM departments_services_offices");
    $final_count = $stmt->fetch()['total'];
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0;'>✅ تم إضافة البيانات التجريبية بنجاح!</h3>";
    echo "<p style='color: #155724; margin: 10px 0 0 0;'>إجمالي السجلات الآن: $final_count</p>";
    echo "</div>";
    
    echo "<p><a href='departments.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى صفحة المديريات</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ خطأ في قاعدة البيانات</h3>";
    echo "<p style='color: #721c24; margin: 10px 0 0 0;'>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
ul { padding-right: 20px; }
li { margin: 5px 0; }
a { margin: 5px; }
</style>
