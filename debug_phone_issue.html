<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة خانة رقم الهاتف</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .debug-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-title {
            background: #e74c3c;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .step-title {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            margin: -15px -15px 10px -15px;
            border-radius: 5px 5px 0 0;
            font-weight: bold;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin: 10px 0;
        }
        
        .result {
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="debug-container">
    <div class="debug-title">🔍 تشخيص مشكلة خانة رقم الهاتف - تحليل مفصل</div>
    
    <div class="highlight">
        <h3>🎯 الهدف من التشخيص:</h3>
        <p>فهم السبب الدقيق لعدم قبول التسجيل في خانة رقم الهاتف وتحديد نقطة المشكلة بالضبط.</p>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 1: اختبار دالة التحقق الحالية</div>
        <p>اختبر الرقم <strong>023554876</strong> مع الدالة الحالية:</p>
        <input type="tel" class="test-input" id="testPhone1" value="023554876" placeholder="023554876">
        <div id="result1"></div>
        
        <div class="code-block">
// الدالة الحالية في suppliers.php
function isValidPhone(phone) {
    if (!phone || phone.trim() === '') {
        return true;
    }
    
    const cleanPhone = phone.replace(/[\s\-\(\)\+\.]/g, '');
    const basicRegex = /^[0-9]{6,15}$/;
    
    return basicRegex.test(cleanPhone);
}
        </div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 2: تحليل خطوة بخطوة</div>
        <p>تحليل مفصل لما يحدث مع الرقم <strong>023554876</strong>:</p>
        <div id="analysis"></div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 3: فحص HTML Input</div>
        <p>فحص خصائص HTML للحقل:</p>
        <div class="code-block">
&lt;input type="tel" class="form-control" id="phone" name="phone"
       placeholder="مثال: 0555123456 أو +213555123456"
       title="أدخل رقم الهاتف"&gt;
        </div>
        <div class="info">
            ✅ لا يوجد pattern صارم في HTML<br>
            ✅ لا يوجد required attribute<br>
            ✅ النوع tel مناسب
        </div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 4: فحص JavaScript Events</div>
        <p>فحص أحداث JavaScript المرتبطة بالحقل:</p>
        <div class="code-block">
document.getElementById('phone').addEventListener('blur', function() {
    const phone = this.value.trim();
    if (phone && phone.length > 0 && !isValidPhone(phone)) {
        this.setCustomValidity('رقم الهاتف يجب أن يحتوي على 6-15 رقم فقط');
    } else {
        this.setCustomValidity('');
    }
});
        </div>
        <div id="eventTest"></div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 5: فحص PHP Backend</div>
        <p>فحص معالجة PHP للبيانات:</p>
        <div class="code-block">
// في suppliers.php - لا يوجد تحقق من رقم الهاتف في PHP
$phone = sanitizeInput($_POST['phone']);

// يتم حفظ الرقم مباشرة في قاعدة البيانات
$stmt->execute([
    $full_name, $commercial_register, $bank_postal_account,
    $tax_id, $statistical_id, $address,
    $phone, $fax, $email, $_POST['id']  // ← رقم الهاتف هنا
]);
        </div>
        <div class="success">
            ✅ PHP لا يحتوي على أي تحقق من رقم الهاتف<br>
            ✅ المشكلة إذن في JavaScript أو HTML
        </div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 6: اختبار مباشر للنموذج</div>
        <p>محاكاة النموذج الفعلي:</p>
        <form id="testForm">
            <label>رقم الهاتف:</label>
            <input type="tel" id="phoneTest" name="phone" class="test-input" placeholder="أدخل رقم الهاتف">
            <button type="submit">اختبار الإرسال</button>
        </form>
        <div id="formResult"></div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 7: سجل التشخيص</div>
        <p>سجل مفصل لجميع العمليات:</p>
        <div id="debugLog" class="log"></div>
    </div>

    <div class="step">
        <div class="step-title">الخطوة 8: الحلول المقترحة</div>
        <div id="solutions"></div>
    </div>

    <script>
        // سجل التشخيص
        const debugLog = document.getElementById('debugLog');
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        // دالة التحقق الحالية
        function isValidPhone(phone) {
            log(`🔍 فحص الرقم: "${phone}"`);
            
            if (!phone || phone.trim() === '') {
                log('✅ الحقل فارغ - مقبول');
                return true;
            }
            
            log(`📝 الرقم الأصلي: "${phone}"`);
            const cleanPhone = phone.replace(/[\s\-\(\)\+\.]/g, '');
            log(`🧹 الرقم بعد التنظيف: "${cleanPhone}"`);
            
            const basicRegex = /^[0-9]{6,15}$/;
            const isValid = basicRegex.test(cleanPhone);
            
            log(`📏 طول الرقم: ${cleanPhone.length}`);
            log(`🔢 يحتوي على أرقام فقط: ${/^[0-9]+$/.test(cleanPhone)}`);
            log(`📐 الطول مناسب (6-15): ${cleanPhone.length >= 6 && cleanPhone.length <= 15}`);
            log(`✅ النتيجة النهائية: ${isValid ? 'مقبول' : 'مرفوض'}`);
            
            return isValid;
        }

        // اختبار الخطوة 1
        document.getElementById('testPhone1').addEventListener('input', function() {
            const phone = this.value;
            const result = document.getElementById('result1');
            const isValid = isValidPhone(phone);
            
            if (isValid) {
                result.innerHTML = '<div class="result success">✅ الرقم صحيح - يجب أن يُقبل</div>';
            } else {
                result.innerHTML = '<div class="result error">❌ الرقم غير صحيح - سيتم رفضه</div>';
            }
        });

        // تحليل الخطوة 2
        function analyzePhone(phone) {
            const analysis = document.getElementById('analysis');
            let html = '<div class="code-block">';
            
            html += `الرقم الأصلي: "${phone}"\n`;
            html += `طول الرقم: ${phone.length}\n`;
            html += `يحتوي على أرقام فقط: ${/^[0-9]+$/.test(phone)}\n`;
            
            const cleanPhone = phone.replace(/[\s\-\(\)\+\.]/g, '');
            html += `الرقم بعد التنظيف: "${cleanPhone}"\n`;
            html += `طول الرقم المنظف: ${cleanPhone.length}\n`;
            
            const basicRegex = /^[0-9]{6,15}$/;
            const isValid = basicRegex.test(cleanPhone);
            html += `يطابق النمط [0-9]{6,15}: ${isValid}\n`;
            
            html += '</div>';
            
            if (isValid) {
                html += '<div class="result success">✅ الرقم يجب أن يُقبل - إذا كان مرفوض فالمشكلة في مكان آخر</div>';
            } else {
                html += '<div class="result error">❌ الرقم مرفوض بسبب عدم مطابقة النمط</div>';
            }
            
            analysis.innerHTML = html;
        }

        // فحص الأحداث
        function testEvents() {
            const eventTest = document.getElementById('eventTest');
            const phoneInput = document.createElement('input');
            phoneInput.type = 'tel';
            phoneInput.value = '023554876';
            phoneInput.className = 'test-input';
            
            phoneInput.addEventListener('blur', function() {
                const phone = this.value.trim();
                log(`🎯 حدث blur: "${phone}"`);
                if (phone && phone.length > 0 && !isValidPhone(phone)) {
                    this.setCustomValidity('رقم الهاتف يجب أن يحتوي على 6-15 رقم فقط');
                    log('❌ تم تعيين رسالة خطأ');
                } else {
                    this.setCustomValidity('');
                    log('✅ تم مسح رسالة الخطأ');
                }
            });
            
            eventTest.appendChild(phoneInput);
            
            setTimeout(() => {
                phoneInput.focus();
                setTimeout(() => {
                    phoneInput.blur();
                }, 100);
            }, 500);
        }

        // اختبار النموذج
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const phone = document.getElementById('phoneTest').value;
            const result = document.getElementById('formResult');
            
            log(`📤 محاولة إرسال النموذج مع الرقم: "${phone}"`);
            
            const phoneInput = document.getElementById('phoneTest');
            const validity = phoneInput.validity;
            const customError = phoneInput.validationMessage;
            
            log(`🔍 حالة الصحة: valid=${validity.valid}`);
            log(`📝 رسالة الخطأ: "${customError}"`);
            
            if (validity.valid) {
                result.innerHTML = '<div class="result success">✅ النموذج صحيح - سيتم الإرسال</div>';
                log('✅ النموذج سيتم إرساله');
            } else {
                result.innerHTML = '<div class="result error">❌ النموذج غير صحيح - لن يتم الإرسال<br>السبب: ' + customError + '</div>';
                log('❌ النموذج لن يتم إرساله: ' + customError);
            }
        });

        // إضافة التحقق للنموذج التجريبي
        document.getElementById('phoneTest').addEventListener('blur', function() {
            const phone = this.value.trim();
            if (phone && phone.length > 0 && !isValidPhone(phone)) {
                this.setCustomValidity('رقم الهاتف يجب أن يحتوي على 6-15 رقم فقط');
            } else {
                this.setCustomValidity('');
            }
        });

        // تشغيل التحليل
        window.onload = function() {
            log('🚀 بدء التشخيص...');
            
            // تحليل الرقم المشكل
            analyzePhone('023554876');
            
            // اختبار الأحداث
            testEvents();
            
            // تشغيل اختبار الخطوة 1
            document.getElementById('testPhone1').dispatchEvent(new Event('input'));
            
            // عرض الحلول
            const solutions = document.getElementById('solutions');
            solutions.innerHTML = `
                <div class="highlight">
                    <h4>🔧 الحلول المقترحة بناءً على التشخيص:</h4>
                    <ol>
                        <li><strong>إذا كانت الدالة تُرجع true:</strong> المشكلة في مكان آخر (ربما cache المتصفح)</li>
                        <li><strong>إذا كانت الدالة تُرجع false:</strong> المشكلة في النمط المستخدم</li>
                        <li><strong>إذا كان النموذج لا يُرسل:</strong> المشكلة في setCustomValidity</li>
                        <li><strong>إذا كان PHP لا يحفظ:</strong> المشكلة في قاعدة البيانات</li>
                    </ol>
                    
                    <h4>🎯 خطوات الحل:</h4>
                    <ul>
                        <li>✅ مسح cache المتصفح</li>
                        <li>✅ إعادة تحميل الصفحة بـ Ctrl+F5</li>
                        <li>✅ فحص console للأخطاء</li>
                        <li>✅ اختبار في متصفح آخر</li>
                    </ul>
                </div>
            `;
            
            log('✅ التشخيص مكتمل');
        };
    </script>
</div>

</body>
</html>
