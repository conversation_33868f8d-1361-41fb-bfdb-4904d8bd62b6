<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';

if (!$id) {
    die('معرف الوصل مطلوب');
}

// جلب بيانات الوصل
$stmt = $db->prepare("
    SELECT ger.*, s.full_name as supplier_name, c.name_ar as currency_name, c.symbol as currency_symbol
    FROM goods_entry_receipts ger 
    LEFT JOIN suppliers s ON ger.supplier_id = s.id 
    LEFT JOIN currencies c ON ger.currency_type = c.code
    WHERE ger.id = ?
");
$stmt->execute([$id]);
$receipt = $stmt->fetch();

if (!$receipt) {
    die('الوصل غير موجود');
}

// جلب المواد
$stmt = $db->prepare("SELECT * FROM goods_entry_items WHERE receipt_id = ? ORDER BY item_number ASC");
$stmt->execute([$id]);
$items = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصل دخول السلع إلى المخزن - <?php echo htmlspecialchars($receipt['receipt_number']); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .header h2 {
            font-size: 16px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .header h3 {
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .info-table {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }
        
        .info-table td {
            padding: 8px;
            border: 1px solid #333;
        }
        
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }
        
        .materials-table th,
        .materials-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
        }
        
        .materials-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .materials-table tfoot td {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .signature-row {
            display: grid;
            grid-template-columns: 33.33% 33.33% 33.33%;
            margin: 30px 0;
            gap: 0;
        }
        
        .signature-box {
            border: none;
            padding: 20px 5px;
            text-align: center;
            min-height: 80px;
            font-size: 12px;
            font-weight: bold;
            color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.3;
        }
        
        .print-buttons {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        @media print {
            body {
                margin: 5mm;
                font-size: 12px;
            }
            
            .print-buttons {
                display: none;
            }
            
            .materials-table {
                font-size: 11px;
            }
            
            .signature-box {
                font-size: 11px;
                padding: 15px 3px;
            }
        }
    </style>
</head>
<body>
    <div class="print-buttons">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-secondary" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="header">
        <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
        <h2>وزارة التربية الوطنية</h2>
        <h3>الديوان الوطني للامتحانات والمسابقات</h3>
    </div>
    
    <table class="info-table">
        <tr>
            <td style="width: 50%; text-align: right; font-weight: bold;">
                رقم الوصل: <?php echo htmlspecialchars($receipt['receipt_number']); ?><br>
                المديرية أو المصلحة: <?php echo htmlspecialchars($receipt['directorate_name']); ?>
            </td>
            <td style="width: 50%; text-align: left; font-weight: bold;">
                التاريخ: <?php echo date('d/m/Y', strtotime($receipt['receipt_date'])); ?><br>
                الممون: <?php echo $receipt['supplier_name'] ? htmlspecialchars($receipt['supplier_name']) : 'غير محدد'; ?>
            </td>
        </tr>
    </table>
    
    <table class="materials-table">
        <thead>
            <tr>
                <th width="5%">الرقم</th>
                <th width="25%">اسم المادة</th>
                <th width="15%">رمز المادة</th>
                <th width="15%">رقم الجرد</th>
                <th width="10%">الكمية</th>
                <th width="12%">السعر الوحدوي</th>
                <th width="13%">المبلغ الإجمالي</th>
                <th width="5%">العملة</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($items as $item): ?>
                <tr>
                    <td><?php echo $item['item_number']; ?></td>
                    <td style="text-align: right; font-weight: bold;">
                        <?php echo htmlspecialchars($item['item_name']); ?>
                    </td>
                    <td><?php echo htmlspecialchars($item['item_code']); ?></td>
                    <td><?php echo htmlspecialchars($item['inventory_number']); ?></td>
                    <td><?php echo number_format($item['quantity'], 2); ?></td>
                    <td><?php echo number_format($item['unit_price'], 2); ?></td>
                    <td><strong><?php echo number_format($item['total_amount'], 2); ?></strong></td>
                    <td><?php echo $receipt['currency_symbol']; ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="6" style="text-align: right; font-weight: bold;">
                    المجموع الإجمالي:
                </td>
                <td style="font-weight: bold; font-size: 14px;">
                    <?php echo number_format($receipt['grand_total'], 2); ?>
                </td>
                <td style="font-weight: bold;">
                    <?php echo $receipt['currency_symbol']; ?>
                </td>
            </tr>
        </tfoot>
    </table>
    
    <div class="signature-row">
        <div class="signature-box">
            إمضاء وختم<br>رئيس مصلحة<br>الوسائل
            <?php if ($receipt['signature_means_chief']): ?>
                <br><small style="color: green;">✓ موقع</small>
            <?php endif; ?>
        </div>
        <div class="signature-box">
            إمضاء وختم<br>المكلف بتسيير<br>المخزن
            <?php if ($receipt['signature_warehouse_manager']): ?>
                <br><small style="color: green;">✓ موقع</small>
            <?php endif; ?>
        </div>
        <div class="signature-box">
            إمضاء وختم<br>المدير الفرعي<br>للإدارة العامة
            <?php if ($receipt['signature_sub_director']): ?>
                <br><small style="color: green;">✓ موقع</small>
            <?php endif; ?>
        </div>
    </div>
    
    <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
        تم إنشاء الوصل في: <?php echo date('d/m/Y H:i', strtotime($receipt['created_at'])); ?>
        <?php if ($receipt['updated_at'] != $receipt['created_at']): ?>
            | آخر تحديث: <?php echo date('d/m/Y H:i', strtotime($receipt['updated_at'])); ?>
        <?php endif; ?>
    </div>
</body>
</html>
