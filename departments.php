<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'إدارة المديريات والمصالح والمكاتب';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// أنواع الوحدات الإدارية
$unit_types = ['مديرية', 'مصلحة', 'مكتب', 'خلية'];

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitizeInput($_POST['name']);
    $internal_number = sanitizeInput($_POST['internal_number'] ?? '');
    $rank = sanitizeInput($_POST['rank'] ?? '');
    $manager_name = sanitizeInput($_POST['manager_name'] ?? '');
    $manager_rank = sanitizeInput($_POST['manager_rank'] ?? '');
    $internal_phone = sanitizeInput($_POST['internal_phone'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $display_order = !empty($_POST['display_order']) ? intval($_POST['display_order']) : 0;
    $type = sanitizeInput($_POST['type']);
    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
    $is_active = isset($_POST['is_active']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            // التحقق من الأعمدة الموجودة
            $stmt_check = $db->query("SHOW COLUMNS FROM departments_services_offices");
            $existing_columns = [];
            while ($row = $stmt_check->fetch(PDO::FETCH_ASSOC)) {
                $existing_columns[] = $row['Field'];
            }

            $has_extended_fields = in_array('internal_number', $existing_columns);

            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                if ($has_extended_fields) {
                    $stmt = $db->prepare("UPDATE departments_services_offices SET
                        name = ?, internal_number = ?, rank = ?, manager_name = ?, manager_rank = ?,
                        internal_phone = ?, email = ?, description = ?, display_order = ?,
                        type = ?, parent_id = ?, is_active = ?
                        WHERE id = ?");

                    $stmt->execute([$name, $internal_number, $rank, $manager_name, $manager_rank,
                                   $internal_phone, $email, $description, $display_order,
                                   $type, $parent_id, $is_active, $_POST['id']]);
                } else {
                    $stmt = $db->prepare("UPDATE departments_services_offices SET
                        name = ?, type = ?, parent_id = ?, is_active = ?
                        WHERE id = ?");

                    $stmt->execute([$name, $type, $parent_id, $is_active, $_POST['id']]);
                }

                $success_message = 'تم تحديث الوحدة الإدارية بنجاح';
            } else {
                // إضافة جديد
                if ($has_extended_fields) {
                    $stmt = $db->prepare("INSERT INTO departments_services_offices
                        (name, internal_number, rank, manager_name, manager_rank, internal_phone,
                         email, description, display_order, type, parent_id, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

                    $stmt->execute([$name, $internal_number, $rank, $manager_name, $manager_rank,
                                   $internal_phone, $email, $description, $display_order,
                                   $type, $parent_id, $is_active]);
                } else {
                    $stmt = $db->prepare("INSERT INTO departments_services_offices
                        (name, type, parent_id, is_active)
                        VALUES (?, ?, ?, ?)");

                    $stmt->execute([$name, $type, $parent_id, $is_active]);
                }

                $success_message = 'تم إضافة الوحدة الإدارية بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        // التحقق من وجود وحدات فرعية
        $stmt = $db->prepare("SELECT COUNT(*) FROM departments_services_offices WHERE parent_id = ?");
        $stmt->execute([$id]);
        $child_count = $stmt->fetchColumn();
        
        if ($child_count > 0) {
            $error_message = 'لا يمكن حذف هذه الوحدة لأنها تحتوي على وحدات فرعية';
        } else {
            $stmt = $db->prepare("DELETE FROM departments_services_offices WHERE id = ?");
            $stmt->execute([$id]);
            $success_message = 'تم حذف الوحدة الإدارية بنجاح';
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT dso.*, parent.name as parent_name
                         FROM departments_services_offices dso
                         LEFT JOIN departments_services_offices parent ON dso.parent_id = parent.id
                         WHERE dso.id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب قائمة الوحدات الأساسية للاختيار كوحدة أب
$stmt = $db->query("SELECT id, name, type FROM departments_services_offices 
                   WHERE type IN ('مديرية', 'مصلحة') AND is_active = 1 
                   ORDER BY type, name ASC");
$parent_units = $stmt->fetchAll();

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    // التحقق من الأعمدة الموجودة للبحث
    $stmt_check = $db->query("SHOW COLUMNS FROM departments_services_offices");
    $existing_columns = [];
    while ($row = $stmt_check->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }

    $search_fields = ["dso.name LIKE ?", "dso.type LIKE ?"];
    $params = ["%$search%", "%$search%"];

    if (in_array('internal_number', $existing_columns)) {
        $search_fields[] = "dso.internal_number LIKE ?";
        $params[] = "%$search%";
    }
    if (in_array('rank', $existing_columns)) {
        $search_fields[] = "dso.rank LIKE ?";
        $params[] = "%$search%";
    }
    if (in_array('manager_name', $existing_columns)) {
        $search_fields[] = "dso.manager_name LIKE ?";
        $params[] = "%$search%";
    }

    $where_clause = "WHERE " . implode(" OR ", $search_fields);
}

// التحقق من وجود العمود display_order
$stmt_check = $db->query("SHOW COLUMNS FROM departments_services_offices LIKE 'display_order'");
$has_display_order = $stmt_check->rowCount() > 0;

$order_clause = $has_display_order ? "ORDER BY dso.display_order ASC, dso.type, dso.name ASC" : "ORDER BY dso.type, dso.name ASC";

$stmt = $db->prepare("SELECT dso.*, parent.name as parent_name, parent.type as parent_type,
                     (SELECT COUNT(*) FROM departments_services_offices WHERE parent_id = dso.id) as child_count
                     FROM departments_services_offices dso
                     LEFT JOIN departments_services_offices parent ON dso.parent_id = parent.id
                     $where_clause
                     $order_clause");
$stmt->execute($params);
$departments = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    إدارة المديريات والمصالح والمكاتب
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate>
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الوحدة الإدارية *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo $edit_data['name'] ?? ''; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الوحدة الإدارية</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">نوع الوحدة *</label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">اختر نوع الوحدة</option>
                                <?php foreach ($unit_types as $unit_type): ?>
                                    <option value="<?php echo $unit_type; ?>" 
                                            <?php echo ($edit_data['type'] ?? '') === $unit_type ? 'selected' : ''; ?>>
                                        <?php echo $unit_type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار نوع الوحدة</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parent_id" class="form-label">الوحدة الأب (اختياري)</label>
                            <select class="form-select" id="parent_id" name="parent_id">
                                <option value="">لا توجد وحدة أب</option>
                                <?php 
                                $current_type = '';
                                foreach ($parent_units as $unit): 
                                    if ($unit['type'] !== $current_type) {
                                        if ($current_type !== '') echo '</optgroup>';
                                        echo '<optgroup label="' . $unit['type'] . '">';
                                        $current_type = $unit['type'];
                                    }
                                ?>
                                    <option value="<?php echo $unit['id']; ?>" 
                                            <?php echo ($edit_data['parent_id'] ?? '') == $unit['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($unit['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                                <?php if ($current_type !== '') echo '</optgroup>'; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3 d-flex align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?php echo ($edit_data['is_active'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">
                                    الوحدة نشطة
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات الإدارية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="internal_number" class="form-label">الرقم الداخلي</label>
                            <input type="text" class="form-control" id="internal_number" name="internal_number"
                                   value="<?php echo $edit_data['internal_number'] ?? ''; ?>"
                                   placeholder="مثال: INT-001">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="rank" class="form-label">الرتبة</label>
                            <select class="form-select" id="rank" name="rank">
                                <option value="">اختر الرتبة</option>
                                <option value="مدير" <?php echo ($edit_data['rank'] ?? '') === 'مدير' ? 'selected' : ''; ?>>مدير</option>
                                <option value="مدير فرعي" <?php echo ($edit_data['rank'] ?? '') === 'مدير فرعي' ? 'selected' : ''; ?>>مدير فرعي</option>
                                <option value="رئيس مصلحة" <?php echo ($edit_data['rank'] ?? '') === 'رئيس مصلحة' ? 'selected' : ''; ?>>رئيس مصلحة</option>
                                <option value="رئيس مكتب" <?php echo ($edit_data['rank'] ?? '') === 'رئيس مكتب' ? 'selected' : ''; ?>>رئيس مكتب</option>
                                <option value="رئيس خلية" <?php echo ($edit_data['rank'] ?? '') === 'رئيس خلية' ? 'selected' : ''; ?>>رئيس خلية</option>
                                <option value="نائب رئيس مصلحة" <?php echo ($edit_data['rank'] ?? '') === 'نائب رئيس مصلحة' ? 'selected' : ''; ?>>نائب رئيس مصلحة</option>
                                <option value="نائب رئيس مكتب" <?php echo ($edit_data['rank'] ?? '') === 'نائب رئيس مكتب' ? 'selected' : ''; ?>>نائب رئيس مكتب</option>
                                <option value="أخرى" <?php echo ($edit_data['rank'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                    </div>

                    <!-- معلومات المسؤول -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="manager_name" class="form-label">اسم المسؤول</label>
                            <input type="text" class="form-control" id="manager_name" name="manager_name"
                                   value="<?php echo $edit_data['manager_name'] ?? ''; ?>"
                                   placeholder="اسم المسؤول عن الوحدة">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="manager_rank" class="form-label">رتبة المسؤول</label>
                            <select class="form-select" id="manager_rank" name="manager_rank">
                                <option value="">اختر الرتبة</option>
                                <option value="أستاذ" <?php echo ($edit_data['manager_rank'] ?? '') === 'أستاذ' ? 'selected' : ''; ?>>أستاذ</option>
                                <option value="أستاذ محاضر" <?php echo ($edit_data['manager_rank'] ?? '') === 'أستاذ محاضر' ? 'selected' : ''; ?>>أستاذ محاضر</option>
                                <option value="أستاذ مساعد" <?php echo ($edit_data['manager_rank'] ?? '') === 'أستاذ مساعد' ? 'selected' : ''; ?>>أستاذ مساعد</option>
                                <option value="دكتور" <?php echo ($edit_data['manager_rank'] ?? '') === 'دكتور' ? 'selected' : ''; ?>>دكتور</option>
                                <option value="مهندس" <?php echo ($edit_data['manager_rank'] ?? '') === 'مهندس' ? 'selected' : ''; ?>>مهندس</option>
                                <option value="مهندس دولة" <?php echo ($edit_data['manager_rank'] ?? '') === 'مهندس دولة' ? 'selected' : ''; ?>>مهندس دولة</option>
                                <option value="مهندس رئيسي" <?php echo ($edit_data['manager_rank'] ?? '') === 'مهندس رئيسي' ? 'selected' : ''; ?>>مهندس رئيسي</option>
                                <option value="تقني سامي" <?php echo ($edit_data['manager_rank'] ?? '') === 'تقني سامي' ? 'selected' : ''; ?>>تقني سامي</option>
                                <option value="تقني" <?php echo ($edit_data['manager_rank'] ?? '') === 'تقني' ? 'selected' : ''; ?>>تقني</option>
                                <option value="إداري" <?php echo ($edit_data['manager_rank'] ?? '') === 'إداري' ? 'selected' : ''; ?>>إداري</option>
                                <option value="أخرى" <?php echo ($edit_data['manager_rank'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="internal_phone" class="form-label">الهاتف الداخلي</label>
                            <input type="text" class="form-control" id="internal_phone" name="internal_phone"
                                   value="<?php echo $edit_data['internal_phone'] ?? ''; ?>"
                                   placeholder="مثال: 123، 1234"
                                   pattern="[0-9]{2,6}"
                                   title="يجب أن يحتوي على أرقام فقط (2-6 أرقام)">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo $edit_data['email'] ?? ''; ?>"
                                   placeholder="<EMAIL>"
                                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="display_order" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="display_order" name="display_order"
                                   value="<?php echo $edit_data['display_order'] ?? 0; ?>"
                                   placeholder="0"
                                   min="0">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="وصف تفصيلي للوحدة الإدارية ومهامها"><?php echo $edit_data['description'] ?? ''; ?></textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="manager_name" class="form-label">اسم المسؤول</label>
                            <input type="text" class="form-control" id="manager_name" name="manager_name"
                                   value="<?php echo $edit_data['manager_name'] ?? ''; ?>"
                                   placeholder="اسم المسؤول عن الوحدة">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="manager_rank" class="form-label">رتبة المسؤول</label>
                            <select class="form-select" id="manager_rank" name="manager_rank">
                                <option value="">اختر الرتبة</option>
                                <option value="أستاذ" <?php echo ($edit_data['manager_rank'] ?? '') === 'أستاذ' ? 'selected' : ''; ?>>أستاذ</option>
                                <option value="أستاذ محاضر" <?php echo ($edit_data['manager_rank'] ?? '') === 'أستاذ محاضر' ? 'selected' : ''; ?>>أستاذ محاضر</option>
                                <option value="أستاذ مساعد" <?php echo ($edit_data['manager_rank'] ?? '') === 'أستاذ مساعد' ? 'selected' : ''; ?>>أستاذ مساعد</option>
                                <option value="دكتور" <?php echo ($edit_data['manager_rank'] ?? '') === 'دكتور' ? 'selected' : ''; ?>>دكتور</option>
                                <option value="مهندس" <?php echo ($edit_data['manager_rank'] ?? '') === 'مهندس' ? 'selected' : ''; ?>>مهندس</option>
                                <option value="مهندس دولة" <?php echo ($edit_data['manager_rank'] ?? '') === 'مهندس دولة' ? 'selected' : ''; ?>>مهندس دولة</option>
                                <option value="مهندس رئيسي" <?php echo ($edit_data['manager_rank'] ?? '') === 'مهندس رئيسي' ? 'selected' : ''; ?>>مهندس رئيسي</option>
                                <option value="تقني سامي" <?php echo ($edit_data['manager_rank'] ?? '') === 'تقني سامي' ? 'selected' : ''; ?>>تقني سامي</option>
                                <option value="تقني" <?php echo ($edit_data['manager_rank'] ?? '') === 'تقني' ? 'selected' : ''; ?>>تقني</option>
                                <option value="إداري" <?php echo ($edit_data['manager_rank'] ?? '') === 'إداري' ? 'selected' : ''; ?>>إداري</option>
                                <option value="أخرى" <?php echo ($edit_data['manager_rank'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="internal_phone" class="form-label">الهاتف الداخلي</label>
                            <input type="text" class="form-control" id="internal_phone" name="internal_phone"
                                   value="<?php echo $edit_data['internal_phone'] ?? ''; ?>"
                                   placeholder="مثال: 123، 1234"
                                   pattern="[0-9]{2,6}"
                                   title="يجب أن يحتوي على أرقام فقط (2-6 أرقام)">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo $edit_data['email'] ?? ''; ?>"
                                   placeholder="<EMAIL>"
                                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="display_order" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="display_order" name="display_order"
                                   value="<?php echo $edit_data['display_order'] ?? 0; ?>"
                                   min="0" placeholder="0">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="وصف مختصر للوحدة الإدارية ومهامها"><?php echo $edit_data['description'] ?? ''; ?></textarea>
                        </div>
                    </div>

                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="departments.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-info" onclick="showOrganizationChart()">
                                    <i class="fas fa-sitemap me-1"></i> الهيكل التنظيمي
                                </button>
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الوحدات الإدارية
                </h5>
                
                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2"
                           placeholder="بحث بالاسم، النوع، الرقم الداخلي، الرتبة أو المسؤول..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="departments.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <?php
                    $stats = [];
                    $colors = ['primary', 'success', 'info', 'warning'];
                    $icons = ['fas fa-building', 'fas fa-cogs', 'fas fa-briefcase', 'fas fa-users'];
                    $i = 0;

                    foreach ($unit_types as $type) {
                        $active_count = count(array_filter($departments, function($dept) use ($type) {
                            return $dept['type'] === $type && $dept['is_active'];
                        }));
                        $total_count = count(array_filter($departments, function($dept) use ($type) {
                            return $dept['type'] === $type;
                        }));
                        $with_manager = count(array_filter($departments, function($dept) use ($type) {
                            return $dept['type'] === $type && !empty($dept['manager_name']);
                        }));

                        $stats[$type] = [
                            'active' => $active_count,
                            'total' => $total_count,
                            'with_manager' => $with_manager,
                            'color' => $colors[$i % 4],
                            'icon' => $icons[$i % 4]
                        ];
                        $i++;
                    }
                    ?>
                    <?php foreach ($stats as $type => $data): ?>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-<?php echo $data['color']; ?> text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-1"><?php echo $data['active']; ?></h4>
                                            <p class="mb-0"><?php echo $type; ?></p>
                                            <small class="opacity-75">
                                                من أصل <?php echo $data['total']; ?> •
                                                <?php echo $data['with_manager']; ?> لديها مسؤول
                                            </small>
                                        </div>
                                        <div>
                                            <i class="<?php echo $data['icon']; ?> fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الرقم الداخلي</th>
                                <th>النوع</th>
                                <th>الرتبة</th>
                                <th>المسؤول</th>
                                <th>الوحدة الأب</th>
                                <th>الوحدات الفرعية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($departments)): ?>
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($departments as $dept): ?>
                                    <tr class="<?php echo !$dept['is_active'] ? 'table-secondary' : ''; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($dept['name']); ?></strong>
                                            <?php if (!empty($dept['description'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($dept['description'], 0, 50)) . (strlen($dept['description']) > 50 ? '...' : ''); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($dept['internal_number'])): ?>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($dept['internal_number']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $dept['type']; ?></span>
                                        </td>
                                        <td>
                                            <?php if (!empty($dept['rank'])): ?>
                                                <span class="badge bg-warning text-dark"><?php echo htmlspecialchars($dept['rank']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($dept['manager_name'])): ?>
                                                <strong><?php echo htmlspecialchars($dept['manager_name']); ?></strong>
                                                <?php if (!empty($dept['manager_rank'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($dept['manager_rank']); ?></small>
                                                <?php endif; ?>
                                                <?php if (!empty($dept['internal_phone'])): ?>
                                                    <br><small class="text-info"><i class="fas fa-phone"></i> <?php echo htmlspecialchars($dept['internal_phone']); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($dept['parent_name']): ?>
                                                <small class="text-muted">
                                                    <span class="badge bg-secondary"><?php echo $dept['parent_type']; ?></span><br>
                                                    <?php echo htmlspecialchars($dept['parent_name']); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($dept['child_count'] > 0): ?>
                                                <span class="badge bg-success"><?php echo $dept['child_count']; ?> وحدة</span>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($dept['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $dept['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($dept['child_count'] == 0): ?>
                                                    <button onclick="deleteDepartment(<?php echo $dept['id']; ?>)" 
                                                            class="btn btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-secondary" disabled title="لا يمكن الحذف - توجد وحدات فرعية">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button onclick="toggleStatus(<?php echo $dept['id']; ?>, <?php echo $dept['is_active'] ? 'false' : 'true'; ?>)" 
                                                        class="btn btn-outline-warning" title="تغيير الحالة">
                                                    <i class="fas fa-toggle-<?php echo $dept['is_active'] ? 'on' : 'off'; ?>"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف الوحدة الإدارية
function deleteDepartment(id) {
    confirmDelete('هل أنت متأكد من حذف هذه الوحدة الإدارية؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// تغيير حالة الوحدة
function toggleStatus(id, newStatus) {
    const statusText = newStatus === 'true' ? 'تفعيل' : 'إلغاء تفعيل';

    confirmAction(`هل أنت متأكد من ${statusText} هذه الوحدة؟`).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب AJAX لتغيير الحالة
            fetch('api/toggle_department_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: id,
                    is_active: newStatus === 'true'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('تم تغيير حالة الوحدة بنجاح');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showError(data.message || 'خطأ في تغيير الحالة');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showError('خطأ في الاتصال بالخادم');
            });
        }
    });
}

// عرض الهيكل التنظيمي
function showOrganizationChart() {
    fetch('api/get_organization_chart.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOrganizationChart(data.chart);
            } else {
                showError('خطأ في جلب الهيكل التنظيمي');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// عرض الهيكل التنظيمي
function displayOrganizationChart(chartData) {
    let chartHtml = '<div class="organization-chart">';

    function buildNode(node, level = 0) {
        const indent = '&nbsp;'.repeat(level * 4);
        const icon = getTypeIcon(node.type);
        const statusClass = node.is_active ? 'text-success' : 'text-muted';

        let html = `
            <div class="org-node level-${level} ${statusClass}" style="margin-bottom: 5px;">
                ${indent}${icon} <strong>${node.name}</strong>
                <span class="badge bg-info">${node.type}</span>
                ${!node.is_active ? '<span class="badge bg-secondary">غير نشط</span>' : ''}
            </div>
        `;

        if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
                html += buildNode(child, level + 1);
            });
        }

        return html;
    }

    chartData.forEach(rootNode => {
        chartHtml += buildNode(rootNode);
    });

    chartHtml += '</div>';

    Swal.fire({
        title: 'الهيكل التنظيمي',
        html: chartHtml,
        width: '800px',
        showConfirmButton: true,
        confirmButtonText: 'إغلاق',
        customClass: {
            htmlContainer: 'text-start'
        }
    });
}

// الحصول على أيقونة حسب النوع
function getTypeIcon(type) {
    const icons = {
        'مديرية': '<i class="fas fa-building text-primary"></i>',
        'مصلحة': '<i class="fas fa-users text-success"></i>',
        'مكتب': '<i class="fas fa-door-open text-info"></i>',
        'خلية': '<i class="fas fa-circle text-warning"></i>'
    };

    return icons[type] || '<i class="fas fa-folder"></i>';
}

// تحديث قائمة الوحدات الأب عند تغيير النوع
document.getElementById('type').addEventListener('change', function() {
    const selectedType = this.value;
    const parentSelect = document.getElementById('parent_id');

    // إخفاء/إظهار قائمة الوحدات الأب حسب النوع
    if (selectedType === 'مديرية') {
        parentSelect.disabled = true;
        parentSelect.value = '';
    } else {
        parentSelect.disabled = false;
    }
});

// تطبيق القاعدة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    if (typeSelect.value === 'مديرية') {
        document.getElementById('parent_id').disabled = true;
    }
});

// إحصائيات متقدمة
function showAdvancedStats() {
    fetch('api/get_department_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let statsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إحصائيات عامة:</h6>
                            <ul class="list-unstyled">
                                <li><strong>إجمالي الوحدات:</strong> ${data.stats.total}</li>
                                <li><strong>الوحدات النشطة:</strong> ${data.stats.active}</li>
                                <li><strong>الوحدات غير النشطة:</strong> ${data.stats.inactive}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>حسب النوع:</h6>
                            <ul class="list-unstyled">
                `;

                Object.entries(data.stats.by_type).forEach(([type, count]) => {
                    statsHtml += `<li><strong>${type}:</strong> ${count}</li>`;
                });

                statsHtml += `
                            </ul>
                        </div>
                    </div>
                `;

                Swal.fire({
                    title: 'إحصائيات مفصلة',
                    html: statsHtml,
                    icon: 'info',
                    confirmButtonText: 'موافق'
                });
            } else {
                showError('خطأ في جلب الإحصائيات');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// إضافة زر الإحصائيات المتقدمة
document.querySelector('.card-header h5').insertAdjacentHTML('afterend', `
    <button type="button" class="btn btn-sm btn-outline-info mt-2" onclick="showAdvancedStats()">
        <i class="fas fa-chart-pie me-1"></i> إحصائيات مفصلة
    </button>
`);

// تصدير الهيكل التنظيمي
function exportOrganizationChart() {
    window.open('export/organization_chart.php', '_blank');
}

// إضافة زر التصدير
document.querySelector('.btn-group[role="group"]').insertAdjacentHTML('beforeend', `
    <button type="button" class="btn btn-info" onclick="exportOrganizationChart()">
        <i class="fas fa-download me-1"></i> تصدير الهيكل
    </button>
`);
</script>

<style>
.organization-chart {
    font-family: 'Noto Sans Arabic', sans-serif;
    line-height: 1.8;
}

.org-node {
    padding: 2px 0;
    border-left: 2px solid #e9ecef;
    margin-left: 10px;
}

.org-node.level-0 {
    border-left: none;
    margin-left: 0;
    font-size: 1.1em;
}

.org-node.level-1 {
    border-left-color: #007bff;
}

.org-node.level-2 {
    border-left-color: #28a745;
}

.org-node.level-3 {
    border-left-color: #ffc107;
}

/* تحسينات الهيكل التنظيمي */
.organization-chart {
    max-height: 600px;
    overflow-y: auto;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.org-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 5px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.org-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.toggle-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.org-children {
    margin-top: 10px;
    padding-right: 20px;
    border-right: 2px dashed #dee2e6;
}

/* ألوان مختلفة للأنواع */
.org-item .badge.bg-info {
    background-color: #17a2b8 !important;
}

.org-item .badge.bg-primary {
    background-color: #007bff !important;
}

.org-item .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* تحسينات الطباعة */
@media print {
    .organization-chart {
        max-height: none;
        overflow: visible;
    }

    .toggle-btn {
        display: none !important;
    }

    .org-children {
        display: block !important;
    }
}
</style>

<!-- Modal الهيكل التنظيمي -->
<div class="modal fade" id="organizationModal" tabindex="-1" aria-labelledby="organizationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="organizationModalLabel">
                    <i class="fas fa-sitemap me-2"></i>الهيكل التنظيمي للمديريات والمصالح
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="expandAll()">
                                <i class="fas fa-expand-arrows-alt me-1"></i>توسيع الكل
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="collapseAll()">
                                <i class="fas fa-compress-arrows-alt me-1"></i>طي الكل
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-success btn-sm" onclick="printOrganizationChart()">
                            <i class="fas fa-print me-1"></i>طباعة الهيكل
                        </button>
                        <button type="button" class="btn btn-info btn-sm" onclick="exportOrganizationChart()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <div id="organizationChart" class="organization-chart">
                    <!-- سيتم تحميل الهيكل التنظيمي هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// عرض الهيكل التنظيمي
function showOrganizationChart() {
    // جلب البيانات وعرض الهيكل
    fetch('get_organization_data.php')
        .then(response => response.json())
        .then(data => {
            buildOrganizationChart(data);
            const modal = new bootstrap.Modal(document.getElementById('organizationModal'));
            modal.show();
        })
        .catch(error => {
            console.error('خطأ في جلب بيانات الهيكل التنظيمي:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ في جلب بيانات الهيكل التنظيمي'
            });
        });
}

// بناء الهيكل التنظيمي
function buildOrganizationChart(data) {
    const chartContainer = document.getElementById('organizationChart');
    chartContainer.innerHTML = '';

    // بناء الهيكل الهرمي
    const hierarchy = buildHierarchy(data);

    // عرض الهيكل
    hierarchy.forEach(item => {
        chartContainer.appendChild(createOrgNode(item, 0));
    });
}

// بناء الهيكل الهرمي من البيانات المسطحة
function buildHierarchy(data) {
    const map = {};
    const roots = [];

    // إنشاء خريطة للعناصر
    data.forEach(item => {
        map[item.id] = { ...item, children: [] };
    });

    // ربط الأطفال بالآباء
    data.forEach(item => {
        if (item.parent_id && map[item.parent_id]) {
            map[item.parent_id].children.push(map[item.id]);
        } else {
            roots.push(map[item.id]);
        }
    });

    return roots;
}

// إنشاء عقدة في الهيكل التنظيمي
function createOrgNode(item, level) {
    const node = document.createElement('div');
    node.className = `org-node level-${level}`;
    node.style.marginLeft = (level * 20) + 'px';

    const hasChildren = item.children && item.children.length > 0;

    node.innerHTML = `
        <div class="d-flex align-items-center mb-2">
            ${hasChildren ? `<button class="btn btn-sm btn-outline-secondary me-2 toggle-btn" onclick="toggleNode(this)">
                <i class="fas fa-minus"></i>
            </button>` : '<span class="me-4"></span>'}

            <div class="org-item flex-grow-1">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1 text-primary">${item.name}</h6>
                        <div class="small text-muted">
                            <span class="badge bg-info me-1">${item.type}</span>
                            ${item.internal_number ? `<span class="badge bg-primary me-1">${item.internal_number}</span>` : ''}
                            ${item.rank ? `<span class="badge bg-warning text-dark">${item.rank}</span>` : ''}
                        </div>
                        ${item.manager_name ? `<div class="small mt-1"><strong>المسؤول:</strong> ${item.manager_name}${item.manager_rank ? ` (${item.manager_rank})` : ''}</div>` : ''}
                        ${item.internal_phone ? `<div class="small text-info"><i class="fas fa-phone"></i> ${item.internal_phone}</div>` : ''}
                    </div>
                    <div class="text-end">
                        <button class="btn btn-sm btn-outline-primary" onclick="editDepartment(${item.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة الأطفال
    if (hasChildren) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'org-children';

        item.children.forEach(child => {
            childrenContainer.appendChild(createOrgNode(child, level + 1));
        });

        node.appendChild(childrenContainer);
    }

    return node;
}

// تبديل عرض/إخفاء العقد الفرعية
function toggleNode(button) {
    const node = button.closest('.org-node');
    const children = node.querySelector('.org-children');
    const icon = button.querySelector('i');

    if (children) {
        if (children.style.display === 'none') {
            children.style.display = 'block';
            icon.className = 'fas fa-minus';
        } else {
            children.style.display = 'none';
            icon.className = 'fas fa-plus';
        }
    }
}

// توسيع جميع العقد
function expandAll() {
    document.querySelectorAll('.org-children').forEach(children => {
        children.style.display = 'block';
    });
    document.querySelectorAll('.toggle-btn i').forEach(icon => {
        icon.className = 'fas fa-minus';
    });
}

// طي جميع العقد
function collapseAll() {
    document.querySelectorAll('.org-children').forEach(children => {
        children.style.display = 'none';
    });
    document.querySelectorAll('.toggle-btn i').forEach(icon => {
        icon.className = 'fas fa-plus';
    });
}

// تعديل قسم من الهيكل التنظيمي
function editDepartment(id) {
    window.location.href = `departments.php?action=edit&id=${id}`;
}

// طباعة الهيكل التنظيمي
function printOrganizationChart() {
    const printContent = document.getElementById('organizationChart').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>الهيكل التنظيمي</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; direction: rtl; }
                .org-node { padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px; }
                .toggle-btn { display: none; }
                @media print { .btn { display: none; } }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                <h2 class="text-center mb-4">الهيكل التنظيمي للمديريات والمصالح</h2>
                ${printContent}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تصدير الهيكل التنظيمي
function exportOrganizationChart() {
    Swal.fire({
        title: 'تصدير الهيكل التنظيمي',
        text: 'اختر تنسيق التصدير',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'PDF',
        cancelButtonText: 'Excel',
        showDenyButton: true,
        denyButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            exportToPDF();
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            exportToExcel();
        }
    });
}

function exportToPDF() {
    // تنفيذ تصدير PDF
    Swal.fire('قريباً', 'ميزة تصدير PDF قيد التطوير', 'info');
}

function exportToExcel() {
    // تنفيذ تصدير Excel
    Swal.fire('قريباً', 'ميزة تصدير Excel قيد التطوير', 'info');
}

// باقي الوظائف الموجودة...
function deleteDepartment(id) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذه الوحدة الإدارية؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

function toggleStatus(id, newStatus) {
    const statusText = newStatus === 'true' ? 'تفعيل' : 'إلغاء تفعيل';

    Swal.fire({
        title: `تأكيد ${statusText}`,
        text: `هل أنت متأكد من ${statusText} هذه الوحدة الإدارية؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب AJAX لتغيير الحالة
            fetch('toggle_department_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: id,
                    status: newStatus === 'true'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

function printPage() {
    window.print();
}
</script>

<?php include 'includes/footer.php'; ?>
